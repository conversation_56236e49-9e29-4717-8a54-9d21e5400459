# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-01"

[[extensions]]
type = "ui_extension"
name = "pos-ui-member-profile"

handle = "pos-ui-member-profile"
description = ""

[extensions.capabilities]
api_access = true
block_progress = true
network_access = true

# Controls where in POS your extension will be injected,
# and the file that contains your extension’s source code.
[[extensions.targeting]]
module = "./src/Tile.tsx"
target = "pos.home.tile.render"

[[extensions.targeting]]
module = "./src/Modal.tsx"
target = "pos.customer-details.action.render"
