import React, {useCallback, useState} from 'react';
import {
  <PERSON>rollView,
  Button,
  Navigator,
  Screen,
  useApi,
  reactExtension,
  Text,
  POSBlock,
  POSBlockRow,
  Stack,
  Badge,
  FormattedTextField
} from '@shopify/ui-extensions-react/point-of-sale';
import type {LoyaltyMemberResponse, OtpResponse} from "./Types";
import {useLoyaltyMember} from "./useLoyaltyMember";
import {serverUrl} from "./Tile";

const discountTiers = [
  {discountTitle: "25%", discountValue: 25},
  {discountTitle: "50%", discountValue: 50},
  {discountTitle: "75%", discountValue: 75},
  {discountTitle: "100%", discountValue: 100},
];

const SmartGridModal = () => {
  const api = useApi<'pos.home.modal.render'>();
  const [isLoadingOTP, setIsLoadingOTP] = useState<boolean>(false);
  const [showOTPInput, setShowOTPInput] = useState<boolean>(false);
  const [reloadMember, setReloadMember] = useState<boolean>(true);
  const [redeemPointsWithOtpResponse, setRedeemPointsWithOtpResponse] = useState<OtpResponse | null>(null);
  const [userOTP, setUserOTP] = useState<string>("");

  const [responseData, setResponseData] = useState<LoyaltyMemberResponse | null>({
    availableFreeShippingDiscountCode: false,
    discountCode: 0,
    member: {}
  });

  const [customerId, setCustomerId] = useState<number | undefined>(api?.cart?.subscribable?.initial?.customer?.id);


  api.cart.subscribable.subscribe((cart) => {
    if (cart?.customer?.id) {
      setCustomerId(cart?.customer?.id);
    }
  });

  const {loading} = useLoyaltyMember(api, {
    customerId: customerId,
    generateFreeShippingDiscountCode: false,
  }, setResponseData, reloadMember, setReloadMember);

  const onPressRedeem = useCallback(async (discountValue: number, discountTitle: string) => {
    if (responseData?.member?.allowedRedeemablePoints && responseData?.member?.mobileNumber) {
      try {
        const sessionToken = await api.session.getSessionToken();
        setIsLoadingOTP(true);
        const response = await fetch(`${serverUrl}/loyalty/points/redeem`, {
          method: "POST",
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${sessionToken}`,
          },
          body: JSON.stringify({
            customerId: customerId,
            mobileNumber: responseData?.member?.mobileNumber,
            pointsAmount: (responseData?.member?.allowedRedeemablePoints / 100) * discountValue,
            action: 'redeemPointsOtpRequest'
          }),
          redirect: 'follow',
        });
        if (response.ok) {
          const data = await response.json();
          setRedeemPointsWithOtpResponse(data);
          setShowOTPInput(true);
        } else {
          throw new Error(`Failed to fetch OTP: ${response.status}`);
        }
      } catch (error) {
        api.toast.show("Failed to redeem points");
        console.error("Error", error);
      } finally {
        setIsLoadingOTP(false);
      }
    }
  }, [api.session, api.toast, customerId, responseData?.member?.allowedRedeemablePoints, responseData?.member?.mobileNumber]);

  const onPressVerifyOTP = useCallback(async () => {
    if (responseData?.member?.allowedRedeemablePoints && responseData?.member?.mobileNumber) {
      try {
        const sessionToken = await api.session.getSessionToken();
        setIsLoadingOTP(true);
        const response = await fetch(`${serverUrl}/loyalty/points/redeem`, {
          method: "POST",
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${sessionToken}`,
          },
          body: JSON.stringify({
            redemptionToken: redeemPointsWithOtpResponse?.shoutoutResult?.otpToken,
            otpCode: userOTP,
            action: 'redeemPointsWithOtp'
          }),
          redirect: 'follow',
        });
        if (response.ok) {
          const data = await response.json();
          if (redeemPointsWithOtpResponse?.discountCode) {
            await api.cart.addCartCodeDiscount(redeemPointsWithOtpResponse?.discountCode);
            setReloadMember(true);
            api.toast.show('Discount code applied');
          } else {
            api.toast.show('Failed to add Discount code');
          }
          setShowOTPInput(false);
        } else {
          throw new Error(`Failed to redeem points: ${response.status}`);
        }
      } catch (error) {
        api.toast.show("Failed to redeem points");
        console.error("Error", error);
      } finally {
        setIsLoadingOTP(false);
      }
    }
  }, [api.cart, api.session, api.toast, responseData?.member?.allowedRedeemablePoints, responseData?.member?.mobileNumber, redeemPointsWithOtpResponse?.discountCode, redeemPointsWithOtpResponse?.shoutoutResult?.otpToken, userOTP]);

  if (!api) {
    return <Text>Loading...</Text>;
  }

  return (
    <Navigator>
      <Screen name='Member' title="Loyalty Member">
        <ScrollView>
          {
            loading ? <>
              <Text>Loading...</Text>
            </> : <>
              {
                responseData?.member && Object.keys(responseData?.member).length === 0 ? <>
                  <POSBlock>
                    <POSBlockRow>
                      <Text color="TextWarning">Unable to fetch loyalty member.</Text>
                    </POSBlockRow>
                  </POSBlock>
                </> : <>
                  {
                    !showOTPInput ? <>
                      <POSBlock>
                        <POSBlockRow>
                          <Stack direction="inline" gap="200" flexChildren>
                            <Stack direction="block" paddingInline="100" gap="200">
                              <Text variant="headingLarge" color="TextSuccess">
                                {responseData?.member?.preferredName || responseData?.member?.firstName || "customerId"}
                              </Text>
                              <Text variant="captionMedium">
                                Mobile Number - {responseData?.member?.mobileNumber || "-"}
                              </Text>
                              <Text variant="captionMedium">
                                email - {responseData?.member?.email || "-"}
                              </Text>
                            </Stack>
                            <Stack
                              direction="block"
                              justifyContent="center"
                              alignContent="end"
                              alignItems="center"
                              paddingInlineStart="200"
                              gap="200"
                            >
                              <Text variant="headingLarge"
                                    color="TextSuccess">{responseData?.member?.allowedRedeemablePoints || 0}</Text>
                              <Badge
                                text={responseData?.member?.status || "-"}
                                variant="success"
                                status="complete"
                              />
                            </Stack>
                          </Stack>
                        </POSBlockRow>
                        {discountTiers.length > 0 ? (
                          <POSBlockRow>
                            <Text variant="headingSmall">Available Discounts:</Text>
                            {discountTiers.map((tier, index) => (
                              <POSBlockRow key={`${tier.discountValue}-${index}`}>
                                <Button
                                  title={`Redeem ${tier.discountTitle}`}
                                  type="primary"
                                  isDisabled={isLoadingOTP}
                                  onPress={() => onPressRedeem(tier.discountValue, tier.discountTitle)}
                                />
                              </POSBlockRow>
                            ))}
                          </POSBlockRow>
                        ) : (
                          <POSBlockRow>
                            <Text variant="headingSmall" color="TextWarning">
                              No available discounts.
                            </Text>
                          </POSBlockRow>
                        )}
                      </POSBlock>
                    </> : <>
                      <POSBlock>
                        <POSBlockRow>
                          <FormattedTextField
                            placeholder="Enter OTP"
                            inputType="number"
                            onChangeText={setUserOTP}
                          />
                          <Button
                            title="Submit"
                            type="primary"
                            isDisabled={isLoadingOTP}
                            onPress={onPressVerifyOTP}
                          />
                        </POSBlockRow>
                      </POSBlock>
                    </>
                  }
                </>
              }
            </>
          }
        </ScrollView>
      </Screen>
    </Navigator>
  )
}

export default reactExtension('pos.home.modal.render', () => {
  return <SmartGridModal/>
})
