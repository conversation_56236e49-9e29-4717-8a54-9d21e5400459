schema {
  query: Input
  mutation: MutationRoot
}

"""
Scale the Functions resource limits based on the field's length.
"""
directive @scaleLimits(rate: Float!) on FIELD_DEFINITION

"""
Requires that exactly one field must be supplied and that field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

"""
Represents a generic custom attribute, such as whether an order is a customer's first.
"""
type Attribute {
  """
  The key or name of the attribute. For example, `"customersFirstOrder"`.
  """
  key: String!

  """
  The value of the attribute. For example, `"true"`.
  """
  value: String
}

"""
Represents information about the buyer that is interacting with the cart.
"""
type BuyerIdentity {
  """
  The customer associated with the cart.
  """
  customer: Customer

  """
  The email address of the buyer that's interacting with the cart.
  """
  email: String

  """
  Whether the buyer authenticated with a customer account.
  """
  isAuthenticated: Boolean!

  """
  The phone number of the buyer that's interacting with the cart.
  """
  phone: String

  """
  The purchasing company associated with the cart.
  """
  purchasingCompany: PurchasingCompany
}

"""
A cart represents the merchandise that a buyer intends to purchase, and the cost associated with the cart.
"""
type Cart {
  """
  The attributes associated with the cart. Attributes are represented as key-value pairs.
  """
  attribute(
    """
    The key of the attribute to retrieve.
    """
    key: String
  ): Attribute

  """
  Information about the buyer that is interacting with the cart.
  """
  buyerIdentity: BuyerIdentity

  """
  The costs that the buyer will pay at checkout.
  """
  cost: CartCost!

  """
  A list of lines containing information about the items that can be delivered.
  """
  deliverableLines: [DeliverableCartLine!]!

  """
  The delivery groups available for the cart based on the buyer's shipping address.
  """
  deliveryGroups: [CartDeliveryGroup!]!

  """
  A list of lines containing information about the items the customer intends to purchase.
  """
  lines: [CartLine!]! @scaleLimits(rate: 0.005)

  """
  The localized fields available for the cart.
  """
  localizedFields(
    """
    The keys of the localized fields to retrieve.
    """
    keys: [LocalizedFieldKey!]! = []
  ): [LocalizedField!]!
}

"""
The cost that the buyer will pay at checkout.
"""
type CartCost {
  """
  The amount, before taxes and discounts, for the customer to pay.
  """
  subtotalAmount: MoneyV2!

  """
  The total amount for the customer to pay.
  """
  totalAmount: MoneyV2!

  """
  The duty amount for the customer to pay at checkout.
  """
  totalDutyAmount: MoneyV2

  """
  The tax amount for the customer to pay at checkout.
  """
  totalTaxAmount: MoneyV2
}

"""
Information about the options available for one or more line items to be delivered to a specific address.
"""
type CartDeliveryGroup {
  """
  A list of cart lines for the delivery group.
  """
  cartLines: [CartLine!]! @scaleLimits(rate: 0.005)

  """
  The destination address for the delivery group.
  """
  deliveryAddress: MailingAddress

  """
  The delivery options available for the delivery group.
  """
  deliveryOptions: [CartDeliveryOption!]!

  """
  Unique identifier for the delivery group.
  """
  id: ID!

  """
  Information about the delivery option the buyer has selected.
  """
  selectedDeliveryOption: CartDeliveryOption
}

"""
Information about a delivery option.
"""
type CartDeliveryOption {
  """
  The code of the delivery option.
  """
  code: String

  """
  The cost for the delivery option.
  """
  cost: MoneyV2!

  """
  The method for the delivery option.
  """
  deliveryMethodType: DeliveryMethod!

  """
  The description of the delivery option.
  """
  description: String

  """
  The unique identifier of the delivery option.
  """
  handle: Handle!

  """
  The title of the delivery option.
  """
  title: String
}

"""
Represents information about the merchandise in the cart.
"""
type CartLine {
  """
  Retrieve a cart line attribute by key.

  Cart line attributes are also known as line item properties in Liquid.
  """
  attribute(
    """
    The key of the attribute to retrieve.
    """
    key: String
  ): Attribute

  """
  The cost of the merchandise line that the buyer will pay at checkout.
  """
  cost: CartLineCost!

  """
  The ID of the cart line.
  """
  id: ID!

  """
  The merchandise that the buyer intends to purchase.
  """
  merchandise: Merchandise!

  """
  The quantity of the merchandise that the customer intends to purchase.
  """
  quantity: Int!

  """
  The selling plan associated with the cart line and the effect that each
  selling plan has on variants when they're purchased.
  """
  sellingPlanAllocation: SellingPlanAllocation
}

"""
The cost of the merchandise line that the buyer will pay at checkout.
"""
type CartLineCost {
  """
  The amount of the merchandise line.
  """
  amountPerQuantity: MoneyV2!

  """
  The compare at amount of the merchandise line. This value varies depending on
  the buyer's identity, and is null when the value is hidden to buyers.
  """
  compareAtAmountPerQuantity: MoneyV2

  """
  The cost of the merchandise line before line-level discounts.
  """
  subtotalAmount: MoneyV2!

  """
  The total cost of the merchandise line.
  """
  totalAmount: MoneyV2!
}

"""
Represents whether the product is a member of the given collection.
"""
type CollectionMembership {
  """
  The ID of the collection.
  """
  collectionId: ID!

  """
  Whether the product is a member of the collection.
  """
  isMember: Boolean!
}

"""
Represents information about a company which is also a customer of the shop.
"""
type Company implements HasMetafields {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was created in Shopify.
  """
  createdAt: DateTime!

  """
  A unique externally-supplied ID for the company.
  """
  externalId: String

  """
  The ID of the company.
  """
  id: ID!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The name of the company.
  """
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was last modified.
  """
  updatedAt: DateTime!
}

"""
A company's main point of contact.
"""
type CompanyContact {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company contact was created in Shopify.
  """
  createdAt: DateTime!

  """
  The ID of the company.
  """
  id: ID!

  """
  The company contact's locale (language).
  """
  locale: String

  """
  The company contact's job title.
  """
  title: String

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company contact was last modified.
  """
  updatedAt: DateTime!
}

"""
A company's location.
"""
type CompanyLocation implements HasMetafields {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company location was created in Shopify.
  """
  createdAt: DateTime!

  """
  A unique externally-supplied ID for the company.
  """
  externalId: String

  """
  The ID of the company.
  """
  id: ID!

  """
  The preferred locale of the company location.
  """
  locale: String

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The name of the company location.
  """
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601))
  at which the company location was last modified.
  """
  updatedAt: DateTime!
}

"""
A country.
"""
type Country {
  """
  The ISO code of the country.
  """
  isoCode: CountryCode!
}

"""
The code designating a country/region, which generally follows ISO 3166-1 alpha-2 guidelines.
If a territory doesn't have a country code value in the `CountryCode` enum, then it might be considered a subdivision
of another country. For example, the territories associated with Spain are represented by the country code `ES`,
and the territories associated with the United States of America are represented by the country code `US`.
"""
enum CountryCode {
  """
  Ascension Island.
  """
  AC

  """
  Andorra.
  """
  AD

  """
  United Arab Emirates.
  """
  AE

  """
  Afghanistan.
  """
  AF

  """
  Antigua & Barbuda.
  """
  AG

  """
  Anguilla.
  """
  AI

  """
  Albania.
  """
  AL

  """
  Armenia.
  """
  AM

  """
  Netherlands Antilles.
  """
  AN

  """
  Angola.
  """
  AO

  """
  Argentina.
  """
  AR

  """
  Austria.
  """
  AT

  """
  Australia.
  """
  AU

  """
  Aruba.
  """
  AW

  """
  Åland Islands.
  """
  AX

  """
  Azerbaijan.
  """
  AZ

  """
  Bosnia & Herzegovina.
  """
  BA

  """
  Barbados.
  """
  BB

  """
  Bangladesh.
  """
  BD

  """
  Belgium.
  """
  BE

  """
  Burkina Faso.
  """
  BF

  """
  Bulgaria.
  """
  BG

  """
  Bahrain.
  """
  BH

  """
  Burundi.
  """
  BI

  """
  Benin.
  """
  BJ

  """
  St. Barthélemy.
  """
  BL

  """
  Bermuda.
  """
  BM

  """
  Brunei.
  """
  BN

  """
  Bolivia.
  """
  BO

  """
  Caribbean Netherlands.
  """
  BQ

  """
  Brazil.
  """
  BR

  """
  Bahamas.
  """
  BS

  """
  Bhutan.
  """
  BT

  """
  Bouvet Island.
  """
  BV

  """
  Botswana.
  """
  BW

  """
  Belarus.
  """
  BY

  """
  Belize.
  """
  BZ

  """
  Canada.
  """
  CA

  """
  Cocos (Keeling) Islands.
  """
  CC

  """
  Congo - Kinshasa.
  """
  CD

  """
  Central African Republic.
  """
  CF

  """
  Congo - Brazzaville.
  """
  CG

  """
  Switzerland.
  """
  CH

  """
  Côte d’Ivoire.
  """
  CI

  """
  Cook Islands.
  """
  CK

  """
  Chile.
  """
  CL

  """
  Cameroon.
  """
  CM

  """
  China.
  """
  CN

  """
  Colombia.
  """
  CO

  """
  Costa Rica.
  """
  CR

  """
  Cuba.
  """
  CU

  """
  Cape Verde.
  """
  CV

  """
  Curaçao.
  """
  CW

  """
  Christmas Island.
  """
  CX

  """
  Cyprus.
  """
  CY

  """
  Czechia.
  """
  CZ

  """
  Germany.
  """
  DE

  """
  Djibouti.
  """
  DJ

  """
  Denmark.
  """
  DK

  """
  Dominica.
  """
  DM

  """
  Dominican Republic.
  """
  DO

  """
  Algeria.
  """
  DZ

  """
  Ecuador.
  """
  EC

  """
  Estonia.
  """
  EE

  """
  Egypt.
  """
  EG

  """
  Western Sahara.
  """
  EH

  """
  Eritrea.
  """
  ER

  """
  Spain.
  """
  ES

  """
  Ethiopia.
  """
  ET

  """
  Finland.
  """
  FI

  """
  Fiji.
  """
  FJ

  """
  Falkland Islands.
  """
  FK

  """
  Faroe Islands.
  """
  FO

  """
  France.
  """
  FR

  """
  Gabon.
  """
  GA

  """
  United Kingdom.
  """
  GB

  """
  Grenada.
  """
  GD

  """
  Georgia.
  """
  GE

  """
  French Guiana.
  """
  GF

  """
  Guernsey.
  """
  GG

  """
  Ghana.
  """
  GH

  """
  Gibraltar.
  """
  GI

  """
  Greenland.
  """
  GL

  """
  Gambia.
  """
  GM

  """
  Guinea.
  """
  GN

  """
  Guadeloupe.
  """
  GP

  """
  Equatorial Guinea.
  """
  GQ

  """
  Greece.
  """
  GR

  """
  South Georgia & South Sandwich Islands.
  """
  GS

  """
  Guatemala.
  """
  GT

  """
  Guinea-Bissau.
  """
  GW

  """
  Guyana.
  """
  GY

  """
  Hong Kong SAR.
  """
  HK

  """
  Heard & McDonald Islands.
  """
  HM

  """
  Honduras.
  """
  HN

  """
  Croatia.
  """
  HR

  """
  Haiti.
  """
  HT

  """
  Hungary.
  """
  HU

  """
  Indonesia.
  """
  ID

  """
  Ireland.
  """
  IE

  """
  Israel.
  """
  IL

  """
  Isle of Man.
  """
  IM

  """
  India.
  """
  IN

  """
  British Indian Ocean Territory.
  """
  IO

  """
  Iraq.
  """
  IQ

  """
  Iran.
  """
  IR

  """
  Iceland.
  """
  IS

  """
  Italy.
  """
  IT

  """
  Jersey.
  """
  JE

  """
  Jamaica.
  """
  JM

  """
  Jordan.
  """
  JO

  """
  Japan.
  """
  JP

  """
  Kenya.
  """
  KE

  """
  Kyrgyzstan.
  """
  KG

  """
  Cambodia.
  """
  KH

  """
  Kiribati.
  """
  KI

  """
  Comoros.
  """
  KM

  """
  St. Kitts & Nevis.
  """
  KN

  """
  North Korea.
  """
  KP

  """
  South Korea.
  """
  KR

  """
  Kuwait.
  """
  KW

  """
  Cayman Islands.
  """
  KY

  """
  Kazakhstan.
  """
  KZ

  """
  Laos.
  """
  LA

  """
  Lebanon.
  """
  LB

  """
  St. Lucia.
  """
  LC

  """
  Liechtenstein.
  """
  LI

  """
  Sri Lanka.
  """
  LK

  """
  Liberia.
  """
  LR

  """
  Lesotho.
  """
  LS

  """
  Lithuania.
  """
  LT

  """
  Luxembourg.
  """
  LU

  """
  Latvia.
  """
  LV

  """
  Libya.
  """
  LY

  """
  Morocco.
  """
  MA

  """
  Monaco.
  """
  MC

  """
  Moldova.
  """
  MD

  """
  Montenegro.
  """
  ME

  """
  St. Martin.
  """
  MF

  """
  Madagascar.
  """
  MG

  """
  North Macedonia.
  """
  MK

  """
  Mali.
  """
  ML

  """
  Myanmar (Burma).
  """
  MM

  """
  Mongolia.
  """
  MN

  """
  Macao SAR.
  """
  MO

  """
  Martinique.
  """
  MQ

  """
  Mauritania.
  """
  MR

  """
  Montserrat.
  """
  MS

  """
  Malta.
  """
  MT

  """
  Mauritius.
  """
  MU

  """
  Maldives.
  """
  MV

  """
  Malawi.
  """
  MW

  """
  Mexico.
  """
  MX

  """
  Malaysia.
  """
  MY

  """
  Mozambique.
  """
  MZ

  """
  Namibia.
  """
  NA

  """
  New Caledonia.
  """
  NC

  """
  Niger.
  """
  NE

  """
  Norfolk Island.
  """
  NF

  """
  Nigeria.
  """
  NG

  """
  Nicaragua.
  """
  NI

  """
  Netherlands.
  """
  NL

  """
  Norway.
  """
  NO

  """
  Nepal.
  """
  NP

  """
  Nauru.
  """
  NR

  """
  Niue.
  """
  NU

  """
  New Zealand.
  """
  NZ

  """
  Oman.
  """
  OM

  """
  Panama.
  """
  PA

  """
  Peru.
  """
  PE

  """
  French Polynesia.
  """
  PF

  """
  Papua New Guinea.
  """
  PG

  """
  Philippines.
  """
  PH

  """
  Pakistan.
  """
  PK

  """
  Poland.
  """
  PL

  """
  St. Pierre & Miquelon.
  """
  PM

  """
  Pitcairn Islands.
  """
  PN

  """
  Palestinian Territories.
  """
  PS

  """
  Portugal.
  """
  PT

  """
  Paraguay.
  """
  PY

  """
  Qatar.
  """
  QA

  """
  Réunion.
  """
  RE

  """
  Romania.
  """
  RO

  """
  Serbia.
  """
  RS

  """
  Russia.
  """
  RU

  """
  Rwanda.
  """
  RW

  """
  Saudi Arabia.
  """
  SA

  """
  Solomon Islands.
  """
  SB

  """
  Seychelles.
  """
  SC

  """
  Sudan.
  """
  SD

  """
  Sweden.
  """
  SE

  """
  Singapore.
  """
  SG

  """
  St. Helena.
  """
  SH

  """
  Slovenia.
  """
  SI

  """
  Svalbard & Jan Mayen.
  """
  SJ

  """
  Slovakia.
  """
  SK

  """
  Sierra Leone.
  """
  SL

  """
  San Marino.
  """
  SM

  """
  Senegal.
  """
  SN

  """
  Somalia.
  """
  SO

  """
  Suriname.
  """
  SR

  """
  South Sudan.
  """
  SS

  """
  São Tomé & Príncipe.
  """
  ST

  """
  El Salvador.
  """
  SV

  """
  Sint Maarten.
  """
  SX

  """
  Syria.
  """
  SY

  """
  Eswatini.
  """
  SZ

  """
  Tristan da Cunha.
  """
  TA

  """
  Turks & Caicos Islands.
  """
  TC

  """
  Chad.
  """
  TD

  """
  French Southern Territories.
  """
  TF

  """
  Togo.
  """
  TG

  """
  Thailand.
  """
  TH

  """
  Tajikistan.
  """
  TJ

  """
  Tokelau.
  """
  TK

  """
  Timor-Leste.
  """
  TL

  """
  Turkmenistan.
  """
  TM

  """
  Tunisia.
  """
  TN

  """
  Tonga.
  """
  TO

  """
  Türkiye.
  """
  TR

  """
  Trinidad & Tobago.
  """
  TT

  """
  Tuvalu.
  """
  TV

  """
  Taiwan.
  """
  TW

  """
  Tanzania.
  """
  TZ

  """
  Ukraine.
  """
  UA

  """
  Uganda.
  """
  UG

  """
  U.S. Outlying Islands.
  """
  UM

  """
  United States.
  """
  US

  """
  Uruguay.
  """
  UY

  """
  Uzbekistan.
  """
  UZ

  """
  Vatican City.
  """
  VA

  """
  St. Vincent & Grenadines.
  """
  VC

  """
  Venezuela.
  """
  VE

  """
  British Virgin Islands.
  """
  VG

  """
  Vietnam.
  """
  VN

  """
  Vanuatu.
  """
  VU

  """
  Wallis & Futuna.
  """
  WF

  """
  Samoa.
  """
  WS

  """
  Kosovo.
  """
  XK

  """
  Yemen.
  """
  YE

  """
  Mayotte.
  """
  YT

  """
  South Africa.
  """
  ZA

  """
  Zambia.
  """
  ZM

  """
  Zimbabwe.
  """
  ZW

  """
  Unknown Region.
  """
  ZZ
}

"""
The three-letter currency codes that represent the world currencies used in
stores. These include standard ISO 4217 codes, legacy codes,
and non-standard codes.
"""
enum CurrencyCode {
  """
  United Arab Emirates Dirham (AED).
  """
  AED

  """
  Afghan Afghani (AFN).
  """
  AFN

  """
  Albanian Lek (ALL).
  """
  ALL

  """
  Armenian Dram (AMD).
  """
  AMD

  """
  Netherlands Antillean Guilder.
  """
  ANG

  """
  Angolan Kwanza (AOA).
  """
  AOA

  """
  Argentine Pesos (ARS).
  """
  ARS

  """
  Australian Dollars (AUD).
  """
  AUD

  """
  Aruban Florin (AWG).
  """
  AWG

  """
  Azerbaijani Manat (AZN).
  """
  AZN

  """
  Bosnia and Herzegovina Convertible Mark (BAM).
  """
  BAM

  """
  Barbadian Dollar (BBD).
  """
  BBD

  """
  Bangladesh Taka (BDT).
  """
  BDT

  """
  Bulgarian Lev (BGN).
  """
  BGN

  """
  Bahraini Dinar (BHD).
  """
  BHD

  """
  Burundian Franc (BIF).
  """
  BIF

  """
  Bermudian Dollar (BMD).
  """
  BMD

  """
  Brunei Dollar (BND).
  """
  BND

  """
  Bolivian Boliviano (BOB).
  """
  BOB

  """
  Brazilian Real (BRL).
  """
  BRL

  """
  Bahamian Dollar (BSD).
  """
  BSD

  """
  Bhutanese Ngultrum (BTN).
  """
  BTN

  """
  Botswana Pula (BWP).
  """
  BWP

  """
  Belarusian Ruble (BYN).
  """
  BYN

  """
  Belarusian Ruble (BYR).
  """
  BYR @deprecated(reason: "`BYR` is deprecated. Use `BYN` available from version `2021-01` onwards instead.")

  """
  Belize Dollar (BZD).
  """
  BZD

  """
  Canadian Dollars (CAD).
  """
  CAD

  """
  Congolese franc (CDF).
  """
  CDF

  """
  Swiss Francs (CHF).
  """
  CHF

  """
  Chilean Peso (CLP).
  """
  CLP

  """
  Chinese Yuan Renminbi (CNY).
  """
  CNY

  """
  Colombian Peso (COP).
  """
  COP

  """
  Costa Rican Colones (CRC).
  """
  CRC

  """
  Cape Verdean escudo (CVE).
  """
  CVE

  """
  Czech Koruny (CZK).
  """
  CZK

  """
  Djiboutian Franc (DJF).
  """
  DJF

  """
  Danish Kroner (DKK).
  """
  DKK

  """
  Dominican Peso (DOP).
  """
  DOP

  """
  Algerian Dinar (DZD).
  """
  DZD

  """
  Egyptian Pound (EGP).
  """
  EGP

  """
  Eritrean Nakfa (ERN).
  """
  ERN

  """
  Ethiopian Birr (ETB).
  """
  ETB

  """
  Euro (EUR).
  """
  EUR

  """
  Fijian Dollars (FJD).
  """
  FJD

  """
  Falkland Islands Pounds (FKP).
  """
  FKP

  """
  United Kingdom Pounds (GBP).
  """
  GBP

  """
  Georgian Lari (GEL).
  """
  GEL

  """
  Ghanaian Cedi (GHS).
  """
  GHS

  """
  Gibraltar Pounds (GIP).
  """
  GIP

  """
  Gambian Dalasi (GMD).
  """
  GMD

  """
  Guinean Franc (GNF).
  """
  GNF

  """
  Guatemalan Quetzal (GTQ).
  """
  GTQ

  """
  Guyanese Dollar (GYD).
  """
  GYD

  """
  Hong Kong Dollars (HKD).
  """
  HKD

  """
  Honduran Lempira (HNL).
  """
  HNL

  """
  Croatian Kuna (HRK).
  """
  HRK

  """
  Haitian Gourde (HTG).
  """
  HTG

  """
  Hungarian Forint (HUF).
  """
  HUF

  """
  Indonesian Rupiah (IDR).
  """
  IDR

  """
  Israeli New Shekel (NIS).
  """
  ILS

  """
  Indian Rupees (INR).
  """
  INR

  """
  Iraqi Dinar (IQD).
  """
  IQD

  """
  Iranian Rial (IRR).
  """
  IRR

  """
  Icelandic Kronur (ISK).
  """
  ISK

  """
  Jersey Pound.
  """
  JEP

  """
  Jamaican Dollars (JMD).
  """
  JMD

  """
  Jordanian Dinar (JOD).
  """
  JOD

  """
  Japanese Yen (JPY).
  """
  JPY

  """
  Kenyan Shilling (KES).
  """
  KES

  """
  Kyrgyzstani Som (KGS).
  """
  KGS

  """
  Cambodian Riel.
  """
  KHR

  """
  Kiribati Dollar (KID).
  """
  KID

  """
  Comorian Franc (KMF).
  """
  KMF

  """
  South Korean Won (KRW).
  """
  KRW

  """
  Kuwaiti Dinar (KWD).
  """
  KWD

  """
  Cayman Dollars (KYD).
  """
  KYD

  """
  Kazakhstani Tenge (KZT).
  """
  KZT

  """
  Laotian Kip (LAK).
  """
  LAK

  """
  Lebanese Pounds (LBP).
  """
  LBP

  """
  Sri Lankan Rupees (LKR).
  """
  LKR

  """
  Liberian Dollar (LRD).
  """
  LRD

  """
  Lesotho Loti (LSL).
  """
  LSL

  """
  Lithuanian Litai (LTL).
  """
  LTL

  """
  Latvian Lati (LVL).
  """
  LVL

  """
  Libyan Dinar (LYD).
  """
  LYD

  """
  Moroccan Dirham.
  """
  MAD

  """
  Moldovan Leu (MDL).
  """
  MDL

  """
  Malagasy Ariary (MGA).
  """
  MGA

  """
  Macedonia Denar (MKD).
  """
  MKD

  """
  Burmese Kyat (MMK).
  """
  MMK

  """
  Mongolian Tugrik.
  """
  MNT

  """
  Macanese Pataca (MOP).
  """
  MOP

  """
  Mauritanian Ouguiya (MRU).
  """
  MRU

  """
  Mauritian Rupee (MUR).
  """
  MUR

  """
  Maldivian Rufiyaa (MVR).
  """
  MVR

  """
  Malawian Kwacha (MWK).
  """
  MWK

  """
  Mexican Pesos (MXN).
  """
  MXN

  """
  Malaysian Ringgits (MYR).
  """
  MYR

  """
  Mozambican Metical.
  """
  MZN

  """
  Namibian Dollar.
  """
  NAD

  """
  Nigerian Naira (NGN).
  """
  NGN

  """
  Nicaraguan Córdoba (NIO).
  """
  NIO

  """
  Norwegian Kroner (NOK).
  """
  NOK

  """
  Nepalese Rupee (NPR).
  """
  NPR

  """
  New Zealand Dollars (NZD).
  """
  NZD

  """
  Omani Rial (OMR).
  """
  OMR

  """
  Panamian Balboa (PAB).
  """
  PAB

  """
  Peruvian Nuevo Sol (PEN).
  """
  PEN

  """
  Papua New Guinean Kina (PGK).
  """
  PGK

  """
  Philippine Peso (PHP).
  """
  PHP

  """
  Pakistani Rupee (PKR).
  """
  PKR

  """
  Polish Zlotych (PLN).
  """
  PLN

  """
  Paraguayan Guarani (PYG).
  """
  PYG

  """
  Qatari Rial (QAR).
  """
  QAR

  """
  Romanian Lei (RON).
  """
  RON

  """
  Serbian dinar (RSD).
  """
  RSD

  """
  Russian Rubles (RUB).
  """
  RUB

  """
  Rwandan Franc (RWF).
  """
  RWF

  """
  Saudi Riyal (SAR).
  """
  SAR

  """
  Solomon Islands Dollar (SBD).
  """
  SBD

  """
  Seychellois Rupee (SCR).
  """
  SCR

  """
  Sudanese Pound (SDG).
  """
  SDG

  """
  Swedish Kronor (SEK).
  """
  SEK

  """
  Singapore Dollars (SGD).
  """
  SGD

  """
  Saint Helena Pounds (SHP).
  """
  SHP

  """
  Sierra Leonean Leone (SLL).
  """
  SLL

  """
  Somali Shilling (SOS).
  """
  SOS

  """
  Surinamese Dollar (SRD).
  """
  SRD

  """
  South Sudanese Pound (SSP).
  """
  SSP

  """
  Sao Tome And Principe Dobra (STD).
  """
  STD @deprecated(reason: "`STD` is deprecated. Use `STN` available from version `2022-07` onwards instead.")

  """
  Sao Tome And Principe Dobra (STN).
  """
  STN

  """
  Syrian Pound (SYP).
  """
  SYP

  """
  Swazi Lilangeni (SZL).
  """
  SZL

  """
  Thai baht (THB).
  """
  THB

  """
  Tajikistani Somoni (TJS).
  """
  TJS

  """
  Turkmenistani Manat (TMT).
  """
  TMT

  """
  Tunisian Dinar (TND).
  """
  TND

  """
  Tongan Pa'anga (TOP).
  """
  TOP

  """
  Turkish Lira (TRY).
  """
  TRY

  """
  Trinidad and Tobago Dollars (TTD).
  """
  TTD

  """
  Taiwan Dollars (TWD).
  """
  TWD

  """
  Tanzanian Shilling (TZS).
  """
  TZS

  """
  Ukrainian Hryvnia (UAH).
  """
  UAH

  """
  Ugandan Shilling (UGX).
  """
  UGX

  """
  United States Dollars (USD).
  """
  USD

  """
  Uruguayan Pesos (UYU).
  """
  UYU

  """
  Uzbekistan som (UZS).
  """
  UZS

  """
  Venezuelan Bolivares (VED).
  """
  VED

  """
  Venezuelan Bolivares (VEF).
  """
  VEF @deprecated(reason: "`VEF` is deprecated. Use `VES` available from version `2020-10` onwards instead.")

  """
  Venezuelan Bolivares Soberanos (VES).
  """
  VES

  """
  Vietnamese đồng (VND).
  """
  VND

  """
  Vanuatu Vatu (VUV).
  """
  VUV

  """
  Samoan Tala (WST).
  """
  WST

  """
  Central African CFA Franc (XAF).
  """
  XAF

  """
  East Caribbean Dollar (XCD).
  """
  XCD

  """
  West African CFA franc (XOF).
  """
  XOF

  """
  CFP Franc (XPF).
  """
  XPF

  """
  Unrecognized currency.
  """
  XXX

  """
  Yemeni Rial (YER).
  """
  YER

  """
  South African Rand (ZAR).
  """
  ZAR

  """
  Zambian Kwacha (ZMW).
  """
  ZMW
}

"""
A custom product.
"""
type CustomProduct {
  """
  Whether the merchandise is a gift card.
  """
  isGiftCard: Boolean!

  """
  Whether the merchandise requires shipping.
  """
  requiresShipping: Boolean!

  """
  The localized title of the product in the customer’s locale.
  """
  title: String!

  """
  The weight of the product variant in the unit system specified with `weight_unit`.
  """
  weight: Float

  """
  Unit of measurement for weight.
  """
  weightUnit: WeightUnit!
}

"""
Represents a customer with the shop.
"""
type Customer implements HasMetafields {
  """
  The total amount of money spent by the customer. Converted from the shop's
  currency to the currency of the cart using a market rate.
  """
  amountSpent: MoneyV2!

  """
  The customer’s name, email or phone number.
  """
  displayName: String!

  """
  The customer’s email address.
  """
  email: String

  """
  The customer's first name.
  """
  firstName: String

  """
  Whether the customer has any of the given tags.
  """
  hasAnyTag(
    """
    The tags to search for.
    """
    tags: [String!]! = []
  ): Boolean!

  """
  Whether the customer has the given tags.
  """
  hasTags(
    """
    The tags to check.
    """
    tags: [String!]! = []
  ): [HasTagResponse!]!

  """
  A unique identifier for the customer.
  """
  id: ID!

  """
  The customer's last name.
  """
  lastName: String

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The number of orders made by the customer.
  """
  numberOfOrders: Int!
}

"""
Represents an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601)-encoded date string.
For example, September 7, 2019 is represented as `"2019-07-16"`.
"""
scalar Date

"""
Represents an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601)-encoded date and time string.
For example, 3:50 pm on September 7, 2019 in the time zone of UTC (Coordinated Universal Time) is
represented as `"2019-09-07T15:50:00Z`".
"""
scalar DateTime

"""
A subset of the [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format that
includes the date and time but not the timezone which is determined from context.

For example, "2018-01-01T00:00:00".
"""
scalar DateTimeWithoutTimezone

"""
A signed decimal number, which supports arbitrary precision and is serialized as a string.

Example values: `"29.99"`, `"29.999"`.
"""
scalar Decimal

"""
Represents information about the merchandise in the cart.
"""
type DeliverableCartLine {
  """
  Retrieve a cart line attribute by key.

  Cart line attributes are also known as line item properties in Liquid.
  """
  attribute(
    """
    The key of the attribute to retrieve.
    """
    key: String
  ): Attribute

  """
  The ID of the cart line.
  """
  id: ID!

  """
  The merchandise that the buyer intends to purchase.
  """
  merchandise: Merchandise!

  """
  The quantity of the merchandise that the customer intends to purchase.
  """
  quantity: Int!
}

"""
The target delivery group.
"""
input DeliveryGroupTarget {
  """
  The ID of the target delivery group.
  """
  id: ID!
}

"""
List of different delivery method types.
"""
enum DeliveryMethod {
  """
  Local Delivery.
  """
  LOCAL

  """
  None.
  """
  NONE

  """
  Shipping to a Pickup Point.
  """
  PICKUP_POINT

  """
  Local Pickup.
  """
  PICK_UP

  """
  Retail.
  """
  RETAIL

  """
  Shipping.
  """
  SHIPPING
}

"""
The target delivery option.
"""
input DeliveryOptionTarget {
  """
  The handle of the target delivery option.
  """
  handle: Handle!
}

"""
The discount to be applied.
"""
input Discount {
  """
  The discount message.
  """
  message: String

  """
  The targets of the discount.
  """
  targets: [Target!]!

  """
  The value of the discount.
  """
  value: Value!
}

"""
A discount wrapper node.
"""
type DiscountNode implements HasMetafields {
  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield
}

"""
A fixed amount value.
"""
input FixedAmount {
  """
  The fixed amount value of the discount, in the currency of the cart.

  The amount must be greater than or equal to 0.
  """
  amount: Decimal!
}

"""
The run target result. In API versions 2023-10 and beyond, this type is deprecated in favor of `FunctionRunResult`.
"""
input FunctionResult {
  """
  The list of discounts to be applied.
  """
  discounts: [Discount!]!
}

"""
The run target result.
"""
input FunctionRunResult {
  """
  The list of discounts to be applied.
  """
  discounts: [Discount!]!
}

"""
A function-scoped handle to a refer a resource.
The Handle type appears in a JSON response as a String, but it is not intended to be human-readable.
Example value: `"10079785100"`
"""
scalar Handle

"""
Represents information about the metafields associated to the specified resource.
"""
interface HasMetafields {
  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield
}

"""
Represents whether the current object has the given tag.
"""
type HasTagResponse {
  """
  Whether the current object has the tag.
  """
  hasTag: Boolean!

  """
  The tag.
  """
  tag: String!
}

"""
Represents a unique identifier, often used to refetch an object.
The ID type appears in a JSON response as a String, but it is not intended to be human-readable.

Example value: `"gid://shopify/Product/10079785100"`
"""
scalar ID

"""
The input object for the function.
"""
type Input {
  """
  The cart.
  """
  cart: Cart!

  """
  The discount node executing the function.
  """
  discountNode: DiscountNode!

  """
  The localization of the Function execution context.
  """
  localization: Localization!

  """
  The conversion rate between the shop's currency and the currency of the cart.
  """
  presentmentCurrencyRate: Decimal!

  """
  Information about the shop.
  """
  shop: Shop!
}

"""
A [JSON](https://www.json.org/json-en.html) object.

Example value:
`{
  "product": {
    "id": "gid://shopify/Product/1346443542550",
    "title": "White T-shirt",
    "options": [{
      "name": "Size",
      "values": ["M", "L"]
    }]
  }
}`
"""
scalar JSON

"""
A language.
"""
type Language {
  """
  The ISO code.
  """
  isoCode: LanguageCode!
}

"""
Language codes supported by Shopify.
"""
enum LanguageCode {
  """
  Afrikaans.
  """
  AF

  """
  Akan.
  """
  AK

  """
  Amharic.
  """
  AM

  """
  Arabic.
  """
  AR

  """
  Assamese.
  """
  AS

  """
  Azerbaijani.
  """
  AZ

  """
  Belarusian.
  """
  BE

  """
  Bulgarian.
  """
  BG

  """
  Bambara.
  """
  BM

  """
  Bangla.
  """
  BN

  """
  Tibetan.
  """
  BO

  """
  Breton.
  """
  BR

  """
  Bosnian.
  """
  BS

  """
  Catalan.
  """
  CA

  """
  Chechen.
  """
  CE

  """
  Central Kurdish.
  """
  CKB

  """
  Czech.
  """
  CS

  """
  Church Slavic.
  """
  CU

  """
  Welsh.
  """
  CY

  """
  Danish.
  """
  DA

  """
  German.
  """
  DE

  """
  Dzongkha.
  """
  DZ

  """
  Ewe.
  """
  EE

  """
  Greek.
  """
  EL

  """
  English.
  """
  EN

  """
  Esperanto.
  """
  EO

  """
  Spanish.
  """
  ES

  """
  Estonian.
  """
  ET

  """
  Basque.
  """
  EU

  """
  Persian.
  """
  FA

  """
  Fulah.
  """
  FF

  """
  Finnish.
  """
  FI

  """
  Filipino.
  """
  FIL

  """
  Faroese.
  """
  FO

  """
  French.
  """
  FR

  """
  Western Frisian.
  """
  FY

  """
  Irish.
  """
  GA

  """
  Scottish Gaelic.
  """
  GD

  """
  Galician.
  """
  GL

  """
  Gujarati.
  """
  GU

  """
  Manx.
  """
  GV

  """
  Hausa.
  """
  HA

  """
  Hebrew.
  """
  HE

  """
  Hindi.
  """
  HI

  """
  Croatian.
  """
  HR

  """
  Hungarian.
  """
  HU

  """
  Armenian.
  """
  HY

  """
  Interlingua.
  """
  IA

  """
  Indonesian.
  """
  ID

  """
  Igbo.
  """
  IG

  """
  Sichuan Yi.
  """
  II

  """
  Icelandic.
  """
  IS

  """
  Italian.
  """
  IT

  """
  Japanese.
  """
  JA

  """
  Javanese.
  """
  JV

  """
  Georgian.
  """
  KA

  """
  Kikuyu.
  """
  KI

  """
  Kazakh.
  """
  KK

  """
  Kalaallisut.
  """
  KL

  """
  Khmer.
  """
  KM

  """
  Kannada.
  """
  KN

  """
  Korean.
  """
  KO

  """
  Kashmiri.
  """
  KS

  """
  Kurdish.
  """
  KU

  """
  Cornish.
  """
  KW

  """
  Kyrgyz.
  """
  KY

  """
  Luxembourgish.
  """
  LB

  """
  Ganda.
  """
  LG

  """
  Lingala.
  """
  LN

  """
  Lao.
  """
  LO

  """
  Lithuanian.
  """
  LT

  """
  Luba-Katanga.
  """
  LU

  """
  Latvian.
  """
  LV

  """
  Malagasy.
  """
  MG

  """
  Māori.
  """
  MI

  """
  Macedonian.
  """
  MK

  """
  Malayalam.
  """
  ML

  """
  Mongolian.
  """
  MN

  """
  Marathi.
  """
  MR

  """
  Malay.
  """
  MS

  """
  Maltese.
  """
  MT

  """
  Burmese.
  """
  MY

  """
  Norwegian (Bokmål).
  """
  NB

  """
  North Ndebele.
  """
  ND

  """
  Nepali.
  """
  NE

  """
  Dutch.
  """
  NL

  """
  Norwegian Nynorsk.
  """
  NN

  """
  Norwegian.
  """
  NO

  """
  Oromo.
  """
  OM

  """
  Odia.
  """
  OR

  """
  Ossetic.
  """
  OS

  """
  Punjabi.
  """
  PA

  """
  Polish.
  """
  PL

  """
  Pashto.
  """
  PS

  """
  Portuguese.
  """
  PT

  """
  Portuguese (Brazil).
  """
  PT_BR

  """
  Portuguese (Portugal).
  """
  PT_PT

  """
  Quechua.
  """
  QU

  """
  Romansh.
  """
  RM

  """
  Rundi.
  """
  RN

  """
  Romanian.
  """
  RO

  """
  Russian.
  """
  RU

  """
  Kinyarwanda.
  """
  RW

  """
  Sanskrit.
  """
  SA

  """
  Sardinian.
  """
  SC

  """
  Sindhi.
  """
  SD

  """
  Northern Sami.
  """
  SE

  """
  Sango.
  """
  SG

  """
  Sinhala.
  """
  SI

  """
  Slovak.
  """
  SK

  """
  Slovenian.
  """
  SL

  """
  Shona.
  """
  SN

  """
  Somali.
  """
  SO

  """
  Albanian.
  """
  SQ

  """
  Serbian.
  """
  SR

  """
  Sundanese.
  """
  SU

  """
  Swedish.
  """
  SV

  """
  Swahili.
  """
  SW

  """
  Tamil.
  """
  TA

  """
  Telugu.
  """
  TE

  """
  Tajik.
  """
  TG

  """
  Thai.
  """
  TH

  """
  Tigrinya.
  """
  TI

  """
  Turkmen.
  """
  TK

  """
  Tongan.
  """
  TO

  """
  Turkish.
  """
  TR

  """
  Tatar.
  """
  TT

  """
  Uyghur.
  """
  UG

  """
  Ukrainian.
  """
  UK

  """
  Urdu.
  """
  UR

  """
  Uzbek.
  """
  UZ

  """
  Vietnamese.
  """
  VI

  """
  Volapük.
  """
  VO

  """
  Wolof.
  """
  WO

  """
  Xhosa.
  """
  XH

  """
  Yiddish.
  """
  YI

  """
  Yoruba.
  """
  YO

  """
  Chinese.
  """
  ZH

  """
  Chinese (Simplified).
  """
  ZH_CN

  """
  Chinese (Traditional).
  """
  ZH_TW

  """
  Zulu.
  """
  ZU
}

"""
Represents limited information about the current time relative to the parent object.
"""
type LocalTime {
  """
  The current date relative to the parent object.
  """
  date: Date!

  """
  Returns true if the current date and time is at or past the given date and time, and false otherwise.
  """
  dateTimeAfter(
    """
    The date and time to compare against, assumed to be in the timezone of the parent object.
    """
    dateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current date and time is before the given date and time, and false otherwise.
  """
  dateTimeBefore(
    """
    The date and time to compare against, assumed to be in the timezone of the parent timezone.
    """
    dateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current date and time is between the two given date and times, and false otherwise.
  """
  dateTimeBetween(
    """
    The upper bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    endDateTime: DateTimeWithoutTimezone!

    """
    The lower bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    startDateTime: DateTimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is at or past the given time, and false otherwise.
  """
  timeAfter(
    """
    The time to compare against, assumed to be in the timezone of the parent timezone.
    """
    time: TimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is at or past the given time, and false otherwise.
  """
  timeBefore(
    """
    The time to compare against, assumed to be in the timezone of the parent timezone.
    """
    time: TimeWithoutTimezone!
  ): Boolean!

  """
  Returns true if the current time is between the two given times, and false otherwise.
  """
  timeBetween(
    """
    The upper bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    endTime: TimeWithoutTimezone!

    """
    The lower bound time to compare against, assumed to be in the timezone of the parent timezone.
    """
    startTime: TimeWithoutTimezone!
  ): Boolean!
}

"""
Information about the localized experiences configured for the shop.
"""
type Localization {
  """
  The country of the active localized experience.
  """
  country: Country!

  """
  The language of the active localized experience.
  """
  language: Language!

  """
  The market of the active localized experience.
  """
  market: Market!
}

"""
Represents the value captured by a localized field. Localized fields are
additional fields required by certain countries on international orders. For
example, some countries require additional fields for customs information or tax
identification numbers.
"""
type LocalizedField {
  """
  The key of the localized field.
  """
  key: LocalizedFieldKey!

  """
  The title of the localized field.
  """
  title: String!

  """
  The value of the localized field.
  """
  value: String
}

"""
Unique key identifying localized fields.
"""
enum LocalizedFieldKey {
  """
  Localized field key 'shipping_credential_br' for country BR.
  """
  SHIPPING_CREDENTIAL_BR

  """
  Localized field key 'shipping_credential_cl' for country CL.
  """
  SHIPPING_CREDENTIAL_CL

  """
  Localized field key 'shipping_credential_cn' for country CN.
  """
  SHIPPING_CREDENTIAL_CN

  """
  Localized field key 'shipping_credential_co' for country CO.
  """
  SHIPPING_CREDENTIAL_CO

  """
  Localized field key 'shipping_credential_cr' for country CR.
  """
  SHIPPING_CREDENTIAL_CR

  """
  Localized field key 'shipping_credential_ec' for country EC.
  """
  SHIPPING_CREDENTIAL_EC

  """
  Localized field key 'shipping_credential_es' for country ES.
  """
  SHIPPING_CREDENTIAL_ES

  """
  Localized field key 'shipping_credential_gt' for country GT.
  """
  SHIPPING_CREDENTIAL_GT

  """
  Localized field key 'shipping_credential_id' for country ID.
  """
  SHIPPING_CREDENTIAL_ID

  """
  Localized field key 'shipping_credential_kr' for country KR.
  """
  SHIPPING_CREDENTIAL_KR

  """
  Localized field key 'shipping_credential_mx' for country MX.
  """
  SHIPPING_CREDENTIAL_MX

  """
  Localized field key 'shipping_credential_my' for country MY.
  """
  SHIPPING_CREDENTIAL_MY

  """
  Localized field key 'shipping_credential_pe' for country PE.
  """
  SHIPPING_CREDENTIAL_PE

  """
  Localized field key 'shipping_credential_pt' for country PT.
  """
  SHIPPING_CREDENTIAL_PT

  """
  Localized field key 'shipping_credential_py' for country PY.
  """
  SHIPPING_CREDENTIAL_PY

  """
  Localized field key 'shipping_credential_tr' for country TR.
  """
  SHIPPING_CREDENTIAL_TR

  """
  Localized field key 'shipping_credential_tw' for country TW.
  """
  SHIPPING_CREDENTIAL_TW

  """
  Localized field key 'shipping_credential_type_co' for country CO.
  """
  SHIPPING_CREDENTIAL_TYPE_CO

  """
  Localized field key 'tax_credential_br' for country BR.
  """
  TAX_CREDENTIAL_BR

  """
  Localized field key 'tax_credential_cl' for country CL.
  """
  TAX_CREDENTIAL_CL

  """
  Localized field key 'tax_credential_co' for country CO.
  """
  TAX_CREDENTIAL_CO

  """
  Localized field key 'tax_credential_cr' for country CR.
  """
  TAX_CREDENTIAL_CR

  """
  Localized field key 'tax_credential_ec' for country EC.
  """
  TAX_CREDENTIAL_EC

  """
  Localized field key 'tax_credential_es' for country ES.
  """
  TAX_CREDENTIAL_ES

  """
  Localized field key 'tax_credential_gt' for country GT.
  """
  TAX_CREDENTIAL_GT

  """
  Localized field key 'tax_credential_id' for country ID.
  """
  TAX_CREDENTIAL_ID

  """
  Localized field key 'tax_credential_it' for country IT.
  """
  TAX_CREDENTIAL_IT

  """
  Localized field key 'tax_credential_mx' for country MX.
  """
  TAX_CREDENTIAL_MX

  """
  Localized field key 'tax_credential_my' for country MY.
  """
  TAX_CREDENTIAL_MY

  """
  Localized field key 'tax_credential_pe' for country PE.
  """
  TAX_CREDENTIAL_PE

  """
  Localized field key 'tax_credential_pt' for country PT.
  """
  TAX_CREDENTIAL_PT

  """
  Localized field key 'tax_credential_py' for country PY.
  """
  TAX_CREDENTIAL_PY

  """
  Localized field key 'tax_credential_tr' for country TR.
  """
  TAX_CREDENTIAL_TR

  """
  Localized field key 'tax_credential_type_co' for country CO.
  """
  TAX_CREDENTIAL_TYPE_CO

  """
  Localized field key 'tax_credential_type_mx' for country MX.
  """
  TAX_CREDENTIAL_TYPE_MX

  """
  Localized field key 'tax_credential_use_mx' for country MX.
  """
  TAX_CREDENTIAL_USE_MX

  """
  Localized field key 'tax_email_it' for country IT.
  """
  TAX_EMAIL_IT
}

"""
Represents a mailing address.
"""
type MailingAddress {
  """
  The first line of the address. Typically the street address or PO Box number.
  """
  address1: String

  """
  The second line of the address. Typically the number of the apartment, suite, or unit.
  """
  address2: String

  """
  The name of the city, district, village, or town.
  """
  city: String

  """
  The name of the customer's company or organization.
  """
  company: String

  """
  The two-letter code for the country of the address. For example, US.
  """
  countryCode: CountryCode

  """
  The first name of the customer.
  """
  firstName: String

  """
  The last name of the customer.
  """
  lastName: String

  """
  The approximate latitude of the address.
  """
  latitude: Float

  """
  The approximate longitude of the address.
  """
  longitude: Float

  """
  The market of the address.
  """
  market: Market

  """
  The full name of the customer, based on firstName and lastName.
  """
  name: String

  """
  A unique phone number for the customer. Formatted using E.164 standard. For example, +16135551111.
  """
  phone: String

  """
  The alphanumeric code for the region. For example, ON.
  """
  provinceCode: String

  """
  The zip or postal code of the address.
  """
  zip: String
}

"""
A market is a group of one or more regions that you want to target for international sales.
By creating a market, you can configure a distinct, localized shopping experience for
customers from a specific area of the world. For example, you can
[change currency](https://shopify.dev/api/admin-graphql/current/mutations/marketCurrencySettingsUpdate),
[configure international pricing](https://shopify.dev/api/examples/product-price-lists),
or [add market-specific domains or subfolders](https://shopify.dev/api/admin-graphql/current/objects/MarketWebPresence).
"""
type Market implements HasMetafields {
  """
  A human-readable unique string for the market automatically generated from its title.
  """
  handle: Handle!

  """
  A globally-unique identifier.
  """
  id: ID!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  A geographic region which comprises a market.
  """
  regions: [MarketRegion!]!
}

"""
Represents a region.
"""
interface MarketRegion {
  """
  The name of the region in the language of the current localization.
  """
  name: String
}

"""
A country which comprises a market.
"""
type MarketRegionCountry implements MarketRegion {
  """
  The two-letter code for the country.
  """
  code: CountryCode!

  """
  The country name in the language of the current localization.
  """
  name: String!
}

"""
The merchandise to be purchased at checkout.
"""
union Merchandise = CustomProduct | ProductVariant

"""
[Metafields](https://shopify.dev/apps/metafields)
enable you to attach additional information to a
Shopify resource, such as a [Product](https://shopify.dev/api/admin-graphql/latest/objects/product)
or a [Collection](https://shopify.dev/api/admin-graphql/latest/objects/collection).
For more information about the Shopify resources that you can attach metafields to, refer to
[HasMetafields](https://shopify.dev/api/admin/graphql/reference/common-objects/HasMetafields).
"""
type Metafield {
  """
  The data stored in the metafield in JSON format.
  """
  jsonValue: JSON!

  """
  The type of data that the metafield stores in the `value` field.
  Refer to the list of [supported types](https://shopify.dev/apps/metafields/types).
  """
  type: String!

  """
  The data stored in the metafield. Always stored as a string, regardless of the metafield's type.
  """
  value: String!
}

"""
A monetary value with currency.
"""
type MoneyV2 {
  """
  Decimal money amount.
  """
  amount: Decimal!

  """
  Currency of the money.
  """
  currencyCode: CurrencyCode!
}

"""
The root mutation for the API.
"""
type MutationRoot {
  """
  Handles the Function result.
  """
  handleResult(
    """
    The result of the Function.
    """
    result: FunctionResult!
  ): Void! @deprecated(reason: "Use the target-specific field instead.")

  """
  Handles the Function result for the purchase.shipping-discount.run target.
  """
  run(
    """
    The result of the Function.
    """
    result: FunctionRunResult!
  ): Void!
}

"""
A percentage value.
"""
input Percentage {
  """
  The percentage value.

  The value is validated against: >= 0 and <= 100.
  """
  value: Decimal!
}

"""
Represents a product.
"""
type Product implements HasMetafields {
  """
  A unique human-friendly string of the product's title.
  """
  handle: Handle!

  """
  Whether the product has any of the given tags.
  """
  hasAnyTag(
    """
    The tags to check.
    """
    tags: [String!]! = []
  ): Boolean!

  """
  Whether the product has the given tags.
  """
  hasTags(
    """
    The tags to check.
    """
    tags: [String!]! = []
  ): [HasTagResponse!]!

  """
  A globally-unique identifier.
  """
  id: ID!

  """
  Whether the product is in any of the given collections.
  """
  inAnyCollection(
    """
    The IDs of the collections to check.
    """
    ids: [ID!]! = []
  ): Boolean!

  """
  Whether the product is in the given collections.
  """
  inCollections(
    """
    The IDs of the collections to check.
    """
    ids: [ID!]! = []
  ): [CollectionMembership!]!

  """
  Whether the product is a gift card.
  """
  isGiftCard: Boolean!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The product type specified by the merchant.
  """
  productType: String

  """
  The localized title of the product in the customer’s locale.
  """
  title: String!

  """
  The name of the product's vendor.
  """
  vendor: String
}

"""
Represents a product variant.
"""
type ProductVariant implements HasMetafields {
  """
  A globally-unique identifier.
  """
  id: ID!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The product that this variant belongs to.
  """
  product: Product!

  """
  Whether the merchandise requires shipping.
  """
  requiresShipping: Boolean!

  """
  An identifier for the product variant in the shop. Required in order to connect to a fulfillment service.
  """
  sku: String

  """
  The localized title of the product variant in the customer’s locale.
  """
  title: String

  """
  The weight of the product variant in the unit system specified with `weight_unit`.
  """
  weight: Float

  """
  Unit of measurement for weight.
  """
  weightUnit: WeightUnit!
}

"""
Represents information about the buyer that is interacting with the cart.
"""
type PurchasingCompany {
  """
  The company associated to the order or draft order.
  """
  company: Company!

  """
  The company contact associated to the order or draft order.
  """
  contact: CompanyContact

  """
  The company location associated to the order or draft order.
  """
  location: CompanyLocation!
}

"""
Represents how products and variants can be sold and purchased.
"""
type SellingPlan implements HasMetafields {
  """
  The description of the selling plan.
  """
  description: String

  """
  A globally-unique identifier.
  """
  id: ID!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield

  """
  The name of the selling plan. For example, '6 weeks of prepaid granola, delivered weekly'.
  """
  name: String!

  """
  Whether purchasing the selling plan will result in multiple deliveries.
  """
  recurringDeliveries: Boolean!
}

"""
Represents an association between a variant and a selling plan. Selling plan
allocations describe the options offered for each variant, and the price of the
variant when purchased with a selling plan.
"""
type SellingPlanAllocation {
  """
  A list of price adjustments, with a maximum of two. When there are two, the
  first price adjustment goes into effect at the time of purchase, while the
  second one starts after a certain number of orders. A price adjustment
  represents how a selling plan affects pricing when a variant is purchased with
  a selling plan. Prices display in the customer's currency if the shop is
  configured for it.
  """
  priceAdjustments: [SellingPlanAllocationPriceAdjustment!]!

  """
  A representation of how products and variants can be sold and purchased. For
  example, an individual selling plan could be '6 weeks of prepaid granola,
  delivered weekly'.
  """
  sellingPlan: SellingPlan!
}

"""
The resulting prices for variants when they're purchased with a specific selling plan.
"""
type SellingPlanAllocationPriceAdjustment {
  """
  The effective price for a single delivery. For example, for a prepaid
  subscription plan that includes 6 deliveries at the price of $48.00, the per
  delivery price is $8.00.
  """
  perDeliveryPrice: MoneyV2!

  """
  The price of the variant when it's purchased with a selling plan For example,
  for a prepaid subscription plan that includes 6 deliveries of $10.00 granola,
  where the customer gets 20% off, the price is 6 x $10.00 x 0.80 = $48.00.
  """
  price: MoneyV2!
}

"""
Information about the shop.
"""
type Shop implements HasMetafields {
  """
  Information about the current time relative to the shop's timezone setting.
  """
  localTime: LocalTime!

  """
  Returns a metafield by namespace and key that belongs to the resource.
  """
  metafield(
    """
    The key for the metafield.
    """
    key: String!

    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String
  ): Metafield
}

"""
The target of the discount.
"""
input Target @oneOf {
  """
  The target delivery group.
  """
  deliveryGroup: DeliveryGroupTarget

  """
  The target delivery option.
  """
  deliveryOption: DeliveryOptionTarget
}

"""
A subset of the [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format that
includes the time but not the date or timezone which is determined from context.
For example, "05:43:21".
"""
scalar TimeWithoutTimezone

"""
The discount value.
"""
input Value @oneOf {
  """
  A fixed amount value.
  """
  fixedAmount: FixedAmount

  """
  A percentage value.
  """
  percentage: Percentage
}

"""
A void type that can be used to return a null value from a mutation.
"""
scalar Void

"""
Units of measurement for weight.
"""
enum WeightUnit {
  """
  Metric system unit of mass.
  """
  GRAMS

  """
  1 kilogram equals 1000 grams.
  """
  KILOGRAMS

  """
  Imperial system unit of mass.
  """
  OUNCES

  """
  1 pound equals 16 ounces.
  """
  POUNDS
}
