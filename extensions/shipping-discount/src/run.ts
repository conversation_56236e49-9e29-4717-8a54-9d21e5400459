import type {
  RunInput,
  FunctionRunResult
} from "../generated/api";

const EMPTY_DISCOUNT: FunctionRunResult = {
  discounts: [],
};


export function run(input: RunInput): FunctionRunResult {
  const customer = input?.cart?.buyerIdentity?.customer;
  if (!customer) {
    return EMPTY_DISCOUNT;
  }
  const freeShippingEligible:boolean = customer.freeShippingEligible?.value === "true";
  const freeShippingExpiryDate:Date|null = customer.freeShippingExpiry?.value ? new Date(customer.freeShippingExpiry.value) : null;
  const currentDateTime = new Date();
  const isFreeShippingValid = freeShippingEligible && freeShippingExpiryDate && freeShippingExpiryDate >= currentDateTime;

  if (isFreeShippingValid) {
    const deliveryOptions = input?.cart?.deliveryGroups
      .flatMap(group => group?.deliveryOptions)
      .map(option => ({
        deliveryOption: {
          handle: option.handle,
        },
      }));

    return {
      discounts: [
        {
          targets: deliveryOptions,
          value: {
            percentage: { value: 100.0 },
          },
        },
      ],
    };
  }
  return EMPTY_DISCOUNT;
};
