import {
  reactExtension,
  TextField,
  Button,
  InlineLayout,
  useTranslate,
  View,
  Text,
  BlockStack,
  useApplyDiscountCodeChange,
  use<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Spinner
} from "@shopify/ui-extensions-react/checkout";
import {useCallback, useEffect, useState, useMemo} from 'react';


//1. Choose an extension target
export default reactExtension("purchase.checkout.reductions.render-before", () => (
  <Extension/>
));

//TODO : check for not registered customer and how UI works.
function Extension() {

  //Hooks
  const translate = useTranslate();
  const applyDiscountCode = useApplyDiscountCodeChange();
  const api = useApi();

  //State
  const [points, setPoints] = useState(null);
  const [pointConfiguration, setPointConfiguration] = useState(null);
  const [memberData, setMemberData] = useState(null);
  const [earnPoints, setEarnPoints] = useState(null);
  const [generalPointSpendingRule, setGeneralPointSpendingRule] = useState(null);

  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState('');
  const [isPointsLocked, setIsPointsLocked] = useState(false);
  const [freeShippingEligible, setFreeShippingEligible] = useState<boolean>(false);
  const [freeShippingDiscountFunctionCode, setFreeShippingDiscountFunctionCode] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMemberData, setIsLoadingMemberData] = useState(false);
  //Constants
  const API_URL = 'https://shopify.beta.loyalty.cxforge.com';//TODO: Use proxy to get the API URL or pass as an environment variable
  const STORE_FRONT_URL = api.shop.storefrontUrl;
  const PRIMARY_KEY = "MOBILE_NUMBER";
  // Fetch data on component mount

  const handleApplyClick = useCallback(async () => {
    const pointsValue = parseInt(inputValue);
    try {
      setIsLoading(true);
      const token = await api.sessionToken.get();
      const response = await fetch(`${API_URL}/api/checkout`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pointsAmount: pointsValue,
          action: 'redeemPoints',
          primaryKey: PRIMARY_KEY,
          memberId: memberData?._id,
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const {discountCode} = await response.json();

      await applyDiscountCode({
        code: discountCode,
        type: "addDiscountCode"
      });
      setError('');
      setIsPointsLocked(true); // Lock the points
      setInputValue(pointsValue.toString()); // Ensure input shows applied value
    } catch (error) {
      setError(translate("errorApplyingPoints"));
      console.error("Error applying points:", error);
    } finally {
      setIsLoading(false);
    }
  }, [inputValue, api.sessionToken, memberData?._id, applyDiscountCode, translate]);


  const handleInputChange = useCallback((value: string) => {
    const pointsValue = parseInt(value);
    if (value && pointsValue < pointConfiguration?.minPointRedemptionAmount) {
      setError(`${translate("pointsMustBeAtLeast", {minPointRedemptionAmount: pointConfiguration?.minPointRedemptionAmount})}`);
    } else if (value && points !== null && parseInt(value) > parseInt(points.toString())) {
      setError(translate("valueMustBeLessThanPoints"));
    } else {
      setInputValue(value);
      setError('');
    }
  }, [points, pointConfiguration?.minPointRedemptionAmount, translate]);


  useEffect(() => {
    const fetchLoyaltyMember = async () => {
      try {
        setIsLoadingMemberData(true);
        const token = await api.sessionToken.get();
        const params = new URLSearchParams({
          primaryKey: PRIMARY_KEY,
          ...(PRIMARY_KEY === "MOBILE_NUMBER" && api?.buyerIdentity?.phone?.current ? {
            mobileNumber: api?.buyerIdentity?.phone?.current ? api.buyerIdentity.phone.current.replace('+', '') : ''
          } : {}),
        });
        const response = await fetch(`${API_URL}/api/checkout?${params.toString()}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        const {
          member: memberData,
          freeShippingEligible,
          freeShippingDiscountFunctionCode,
          pointConfiguration,
          generalSpendingRule
        } = await response.json();
        setPointConfiguration(pointConfiguration);
        setMemberData(memberData);
        setGeneralPointSpendingRule(generalSpendingRule);
        setPoints(memberData?.allowedRedeemablePoints ?? 0);
        if (freeShippingEligible) {
          setFreeShippingEligible(freeShippingEligible);
          setFreeShippingDiscountFunctionCode(freeShippingDiscountFunctionCode);
        }
      } catch (err) {
        setError(translate("errorFetchingLoyaltyMemberData"));
        console.error("Error fetching loyalty member data:", err);
      } finally {
        setIsLoadingMemberData(false);
      }
    };

    fetchLoyaltyMember();

  }, [api.buyerIdentity.phone, api.sessionToken, translate]);

  useEffect(() => {
    if (api?.cost?.subtotalAmount?.current?.amount && generalPointSpendingRule) {
      const subtotalAmount = api.cost.subtotalAmount.current.amount || 0;
      let amountPerPoint = 0;
      if (generalPointSpendingRule?.ruleData?.pointAmountsForRangesEnabled) {
        const pointRange = generalPointSpendingRule?.ruleData?.pointAmountMappingsForRanges?.sort(
          (a: any, b: any) => a.billValueMargin - b.billValueMargin
        ).find((range: any) => (
          subtotalAmount >= range?.billValueMargin
        ));
        amountPerPoint = pointRange?.amountPerPoint || 0;
      } else {
        amountPerPoint = generalPointSpendingRule?.ruleData?.amountPerPoint || 0;
      }
      const pointsValue = Math.floor(api.cost.subtotalAmount.current.amount / amountPerPoint);
      setEarnPoints(pointsValue);
    }
  }, [api.cost.subtotalAmount, generalPointSpendingRule]);

  const isApplyButtonDisabled = useMemo(() => {
    return (
      isLoading ||
      isPointsLocked ||
      !inputValue ||
      parseInt(inputValue) < (pointConfiguration?.minPointRedemptionAmount || 100) ||
      parseInt(inputValue) > (pointConfiguration?.maxPointRedemptionAmount || 100)
    );
  }, [isLoading, isPointsLocked, inputValue, pointConfiguration]);

  return (
    <>
      {memberData ? <>
        {points !== null ? <BlockStack inlineAlignment="start">
          <Text size="large">{translate("redeemPointsTitle")}</Text>
          <Text size="base">
            {points !== null
              ? translate("askHowMuchToRedeem", {
                points,
                maxPointRedemptionAmount: pointConfiguration?.maxPointRedemptionAmount || 0,
                minPointRedemptionAmount: pointConfiguration?.minPointRedemptionAmount || 0
              })
              : ""}
          </Text>
        </BlockStack> : <BlockStack inlineAlignment="center">
          <Spinner/>
        </BlockStack>}

        <InlineLayout columns={['fill', '15%']} padding={['base', 'none', 'base', 'none']}>
          <View border="none" padding={['none', 'base', 'none', 'none']}>
            <TextField
              label="Points amount"
              value={inputValue}
              onChange={handleInputChange}
              error={error}
              disabled={isPointsLocked}
            />
          </View>
          <Button
            kind="primary"
            onPress={handleApplyClick}
            disabled={isApplyButtonDisabled}
          >
            {isLoading ? <Spinner/> : translate("apply")}
          </Button>
        </InlineLayout>
        {earnPoints !== null ? <Banner
          status="info"
          title={points !== null
            ? `${translate("saidaHowMuchCanEarnFromPurchase", {earnPoints})}`
            : ""}
        /> : <BlockStack inlineAlignment="center">
          <Spinner/>
        </BlockStack>}
        {freeShippingEligible && freeShippingDiscountFunctionCode && (
          <BlockStack inlineAlignment="start">
            <Text size="large">{translate("freeShippingTitle")}</Text>
            <Text size="base">{translate("freeShippingEligible", {
              freeShippingDiscountFunctionCode: (
                <Badge tone="subdued">
                  {freeShippingDiscountFunctionCode}
                </Badge>
              )
            })}</Text>
          </BlockStack>
        )}
      </> : <>
        {earnPoints !== null ? <Banner status="info">
          <Text>
            {translate("earnPointsMessage", {
              earnPoints, logInLink: (
                <Link to={`${STORE_FRONT_URL}/account/login`}>
                  Log in
                </Link>
              ), signUpLink: (<Link to={`${STORE_FRONT_URL}/account/register`}>
                Sign up
              </Link>)
            })}
          </Text>
        </Banner> : <BlockStack inlineAlignment="center">
          {!isLoadingMemberData && error ? <Banner status="critical">
            <Text>
              {error}
            </Text>
          </Banner> : <Spinner/>}
        </BlockStack>}

      </>}
    </>
  );
}
