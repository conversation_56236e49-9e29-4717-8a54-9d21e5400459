import type {
  RunInput,
  FunctionRunResult
} from "../generated/api";
import {
  DiscountApplicationStrategy,
} from "../generated/api";

const EMPTY_DISCOUNT: FunctionRunResult = {
  discountApplicationStrategy: DiscountApplicationStrategy.First,
  discounts: [],
};

type Configuration = {};
//Below commented is for the checkout extension
// export function run(input: RunInput): FunctionRunResult {
//   const pointstoRedeem = input.cart?.attribute?.value;
//   const discountAmount = parseFloat(pointstoRedeem || "0");
//   if(discountAmount > 0) {
//   return {
//     discountApplicationStrategy:DiscountApplicationStrategy.First,
//     discounts: [{
//       message: `redeem points`,
//       value : {
//         fixedAmount : {
//           amount : discountAmount
//         }
//       },
//       targets: [
//         {
//           orderSubtotal: {
//             excludedVariantIds:[]
//           }
//         }
//       ]
//     }]
//   }
// }
//   return EMPTY_DISCOUNT;
// };

/**
 * @param {RunInput} input
 * @returns {FunctionRunResult}
 */
export function run(input: RunInput): FunctionRunResult {
  /**
   * @type {{
   *   quantity: number
   * }}
   */
  const configuration = JSON.parse(
    input?.discountNode?.metafield?.value ?? "{}",
  );
  const pointstoRedeem = input.cart?.attribute?.value;
  const discountAmount = parseFloat(pointstoRedeem || "0");
  let points = 0;
  if(discountAmount > 0) {
    points = discountAmount;  
  }
  else if(configuration.quantity){
    points = configuration.quantity;
  }
  else{
    return EMPTY_DISCOUNT;
  }
  return {
        discountApplicationStrategy:DiscountApplicationStrategy.First,
        discounts: [{
          message: `redeem points`,
          value : {
            fixedAmount : {
              amount : points
            }
          },
          targets: [
            {
              orderSubtotal: {
                excludedVariantIds:[]
              }
            }
          ]
        }]
      }
}
