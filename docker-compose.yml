version: '3'

services:

  base:
    build:
      context: .
      dockerfile: Dockerfile
    image: shopify
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - NODE_ENV=production
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_REGION=us-west-2

  app-public:
    image: shopify
    ports:
      - "81:3001"
      - "3001:3001"
    environment:
      - PORT=3001
    env_file:
      - .env.public
    restart: unless-stopped
    command: ["npm", "run", "docker-start"]
  app-private:
    image: shopify
    ports:
      - "80:3000"
      - "3000:3000"
    env_file:
      - .env
    restart: unless-stopped
    command: ["npm", "run", "docker-start"]
