# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "54f229f96d4762a8bf316c5b41e5c0c9"
application_url = "https://shopify.beta.loyalty.cxforge.com"
embedded = true
name = "cxforge-loyalty-beta"
handle = "cxforge-loyalty-beta"

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = false

[webhooks]
api_version = "2025-01"
  [[webhooks.subscriptions]]
  uri = "https://shopify.beta.loyalty.cxforge.com/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://shopify.beta.loyalty.cxforge.com/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://shopify.beta.loyalty.cxforge.com/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "https://shopify.beta.loyalty.cxforge.com/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "customers/create", "orders/create" ]
  uri = "arn:aws:events:us-west-2::event-source/aws.partner/shopify.com/220349333505/shopify_cxforge_webhook_beta"



[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_discounts,read_orders,write_customers,write_discounts,write_products"

[auth]
redirect_urls = [
  "https://shopify.beta.loyalty.cxforge.com/auth/callback",
  "https://shopify.beta.loyalty.cxforge.com/auth/shopify/callback",
  "https://shopify.beta.loyalty.cxforge.com/api/auth/callback"
]

[pos]
embedded = true


[app_proxy]
url = "https://shopify.beta.loyalty.cxforge.com/proxy"
prefix = "apps"
subpath = "members"
