version: '3'

services:

  base:
    build:
      context: .
      dockerfile: Dockerfile
    image: shopify_beta
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_REGION=us-west-2

  app-beta:
    image: shopify_beta  
    ports:
      - "82:3002"
      - "3002:3002"
    environment:
      - PORT=3002
    env_file:
      - .env.beta
    restart: unless-stopped
    command: ["npm", "run", "docker-start"]