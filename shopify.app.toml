# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "823b7dcecb86dddbfef7775fee315635"
name = "shoutout-loyalty-integration"
handle = "shoutout-loyalty-integration"
application_url = "https://hawaii-determines-seventh-popular.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "commercial-app.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_discounts,read_orders,write_customers,write_discounts,write_products"

[auth]
redirect_urls = [
  "https://hawaii-determines-seventh-popular.trycloudflare.com/auth/callback",
  "https://hawaii-determines-seventh-popular.trycloudflare.com/auth/shopify/callback",
  "https://hawaii-determines-seventh-popular.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

  [[webhooks.subscriptions]]
  uri = "/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "arn:aws:events:us-west-2::event-source/aws.partner/shopify.com/162119712769/shopify-so-loyalty"

[app_proxy]
url = "https://hawaii-determines-seventh-popular.trycloudflare.com/proxy"
subpath = "members"
prefix = "apps"

[pos]
embedded = false
