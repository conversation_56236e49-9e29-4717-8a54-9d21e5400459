FROM node:20-alpine as base

# Install required packages
RUN apk add --no-cache \
    openssl \
    curl

EXPOSE 3000

WORKDIR /app

ENV NODE_ENV=production

COPY package*.json ./
COPY prisma ./prisma/

RUN npm ci --omit=dev && npm cache clean --force
# Remove CLI packages since we don't need them in production by default.
# Remove this line if you want to run CLI commands in your container.
RUN npm remove @shopify/cli

RUN npm install @prisma/client

COPY . .

RUN npm run build

