import Joi from "joi";

interface ValidationResult<T> {
  value?: T;
  error?: string;
  isValid: boolean;
}

class ProxyValidator {

  static discountCodesSchema = Joi.object({
    admin: Joi.object().required(),
    customerId: Joi.string().required(),
  });

  static loaderDefaultSchema = Joi.object({
    customerId: Joi.string().allow("").optional(),
    shop: Joi.string().required(),
    primaryKey: Joi.string().valid("MOBILE_NUMBER",'CARD_NUMBER').required(),
    mobileNumber: Joi.string().allow("").optional(),
  }).or("customerId", "mobileNumber");

  static registerWithOtpRequestSchema = Joi.object({
    mobileNumber: Joi.string().required(),
    customerId: Joi.string().required(),
    shop: Joi.string().required(),
    admin: Joi.object().required(),
    session: Joi.object().required(),
    primaryKey: Joi.string().valid("MOBILE_NUMBER",'CARD_NUMBER').required(),
  });

  static registerWithOtpSchema = Joi.object({
    registerMemberToken: Joi.string().required(),
    otpCode: Joi.string().required(),
    shop: Joi.string().required(),
  });

  static redeemPointsWithOtpRequestSchema = Joi.object({
    pointsAmount: Joi.number().required(),
    memberId: Joi.string().required(),
    customerId: Joi.number().required(),
    shop: Joi.string().required(),
    action: Joi.string().valid('redeemPoints', 'redeemPointsWithOtpRequest').required(),
    admin: Joi.object().allow(null),
  });

  static redeemPointsWithOtpSchema = Joi.object({
    redemptionToken: Joi.string().required(),
    otpCode: Joi.string().required(),
    shop: Joi.string().required(),
  });

  static generateFreeShippingCouponSchema = Joi.object({
    shop: Joi.string().required(),
    admin: Joi.object().required(),
    customerId: Joi.number().required(),
    memberId: Joi.string().required(),
  });

  static validateDiscountCodes(params: any): ValidationResult<any> {
    const {value, error} = this.discountCodesSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateLoaderDefault(params: any): ValidationResult<any> {
    const {value, error} = this.loaderDefaultSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateRegisterWithOtpRequest(params: any): ValidationResult<any> {
    const {value, error} = this.registerWithOtpRequestSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateRegisterWithOtp(params: any): ValidationResult<any> {
    const {value, error} = this.registerWithOtpSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateRedeemPointsWithOtpRequest(params: any): ValidationResult<any> {
    const {value, error} = this.redeemPointsWithOtpRequestSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateRedeemPointsWithOtp(params: any): ValidationResult<any> {
    const {value, error} = this.redeemPointsWithOtpSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }

  static validateGenerateFreeShippingCoupon(params: any): ValidationResult<any> {
    const {value, error} = this.generateFreeShippingCouponSchema.validate(params);
    if (error) {
      return { error: error.message, isValid: false };
    }
    return { value, isValid: true };
  }
}

export default ProxyValidator;
