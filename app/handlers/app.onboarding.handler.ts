

import ShoutoutIdentityService from "app/services/shoutout.identity.service";
import ApiException from "app/utils/api.exception";
import ShoutoutLoyaltyService from "app/services/shoutout.loyalty.service.v2";
import { OrganizationCreateData } from "app/models/organization/organization.data";
import ObjectUtils from "app/utils/object.utils";
import { OrganizationCreateStatus } from "@prisma/client";
import { ICreatePointRule } from "app/types/shoutout.pointrules";


type ReturnType<T> = {
    error?: Error,
    response?: T,
    organizationCreateStatus: Partial<OrganizationCreateStatus>
};

class AppOnboardingHandler {

    /** {"type":"GLOBAL","name":"Shopify Integration (Auto Generated)",
    "description":"Auto generated Shopify Integration Permission Group. Do not delete",
    "policies":[{"moduleId":"62020d15b9ce100765fdddab","actions":["GetOrganization"]},
    {"moduleId":"62020d23c348b93f73b5e8fb","actions":["ListRegions"]},
    {"moduleId":"62020d3343d7bf3156c45a84","actions":["CollectPointsBill","CollectPointsAmount","RedeemPoints","AdjustPoints","ReverseBill","ReverseBillWithOtp"]},
    {"moduleId":"62020d00b0c5fa75def36fe3","actions":["GetMember","ListMembers","CreateMember","RegisterMember","UpdateMember"]},
    {"moduleId":"62020ce8224dc01c817440ae","actions":["ListCards","GetCard","AssignCard","GenerateCards"]}]}
    */

    public static async createPermissionGroup(userToken: string): Promise<ReturnType<string>> {

        const modules = await ShoutoutIdentityService.getModules(userToken);
        if (modules?.items?.length > 0) {
            const modulesMap = new Map<string, string>(Array.from(modules.items, (item: { moduleName: string, _id: string }) => [item.moduleName, item._id]));

            const groupCreationResponse: { _id: string } = await ShoutoutIdentityService.createPermissionGroup({
                type: 'GLOBAL',
                name: 'Shopify Integration (Auto Generated)',
                description: 'Auto generated Shopify Integration Permission Group. Do not delete',
                policies: [
                    {
                        moduleId: modulesMap.get('Card') || '',
                        actions: ["ListCards", "GetCard", "AssignCard", "GenerateCards"]
                    },
                    {
                        moduleId: modulesMap.get('Member') || '',
                        actions: ["GetMember", "ListMembers", "CreateMember", "RegisterMember", "UpdateMember", "RegisterMemberWithOtp"]
                    },
                    {
                        moduleId: modulesMap.get('Klip') || '',
                        actions: ["GetMember", "ListMembers"]
                    },
                    {
                        moduleId: modulesMap.get('Point') || '',
                        actions: ["CollectPointsBill", "CollectPointsAmount", "RedeemPoints", "AdjustPoints", "ReverseBill", "ReverseBillWithOtp"]
                    },
                    {
                        moduleId: modulesMap.get('Region') || '',
                        actions: ["ListRegions"]
                    },
                    {
                        moduleId: modulesMap.get('Organization') || '',
                        actions: ["GetOrganization"]
                    },
                    {
                        moduleId: modulesMap.get('Tier') || '',
                        actions: ["ListTiers"]
                    }
                ],
            }, userToken);

            if (groupCreationResponse._id) {
                return {

                    response: groupCreationResponse._id,
                    organizationCreateStatus: {
                        permissionGroupId: groupCreationResponse._id
                    }
                };
            }
        }

        return {
            error: new Error('Failed to create permission group'),
            organizationCreateStatus: {}
        };


    }

    public static async createIntegrationClient(organizationId: string, userToken: string): Promise<ReturnType<{
        clientId: string,
        clientSecret: string
    }>> {

        let organizationCreateStatus: Partial<OrganizationCreateStatus> = {};
        try {
            const integrationClientResponse = await ShoutoutIdentityService.createIntegrationClient(userToken, organizationId);
            organizationCreateStatus.integrationClientId = integrationClientResponse.clientData.clientId;
            organizationCreateStatus.integrationClientSecret = integrationClientResponse.clientData.clientSecret;
            organizationCreateStatus.integrationId = integrationClientResponse.id;


            return {
                response: {
                    clientId: integrationClientResponse.clientData.clientId,
                    clientSecret: integrationClientResponse.clientData.clientSecret
                },
                organizationCreateStatus: organizationCreateStatus
            };
        } catch (error) {

            return {
                error: new Error('Failed to create integration client'),
                organizationCreateStatus: organizationCreateStatus
            };
        }
    }
    public static async assignPermissions(clientId: string, permissionGroupId: string, regionId: string, userToken: string): Promise<ReturnType<string>> {
        try {
            await ShoutoutIdentityService.addIntegrationClientPermissionGroup(clientId, permissionGroupId, regionId, userToken);
            return {
                response: 'Permissions assigned',
                organizationCreateStatus: { assignedPermissions: true }
            };
        } catch (error) {
            return {
                error: new Error('Failed to assign permissions'),
                organizationCreateStatus: { assignedPermissions: false }
            };

        }

    }

    public static async signupUser(customerEmail: string, password: string, details: any): Promise<ReturnType<string>> {
        try {
            const signupResponse = await ShoutoutIdentityService.signup({
                userData: {
                    username: customerEmail,
                    firstName: details.firstName || '',
                    lastName: details.lastName || '',

                    contact: {
                        email: customerEmail,
                        ...(details.mobileNumber && { mobileNumber: details.mobileNumber }),
                        address: {
                            city: details.address.city,
                            street: `${details.address.line1},${details.address.line2}`,
                            zip: details.address.zipOrPostcode,
                        }
                    },
                    password
                },
                emailVerified: true
            });


            return {
                response: signupResponse._id,
                organizationCreateStatus: {
                    rootUserId: signupResponse._id,
                    email: customerEmail,
                    password: password,
                    organizationId: signupResponse.organizationId
                }
            };
        } catch (error) {
            console.error(error, "Failed to signup user");
            return {
                error: new Error('Failed to signup user'),
                organizationCreateStatus: {
                    rootUserId: '',
                }
            };
        }
    }

    public static async getUserToken(email: string, password: string): Promise<ReturnType<string>> {
        try {
            const userTokenResponse = await ShoutoutIdentityService.getUserToken(email, password);
            if (!userTokenResponse.access_token) {
                return {
                    error: new Error("Failed to retrieve the user token"),
                    organizationCreateStatus: {}
                };
            }

            return {
                response: userTokenResponse.access_token,
                organizationCreateStatus: {}
            };
        } catch (error) {

            console.error(error, "Failed to retrieve user token");
            return {
                error: new Error('Failed to retrieve user token'),
                organizationCreateStatus: {}
            };
        }
    }

    public static async createOrganization(details: any, customerEmail: string, primaryAttribute: string, pointsPerCurrency: number, userToken: string): Promise<ReturnType<any>> {
        try {

            const organizationCreateRequest: OrganizationCreateData = {
                organizationName: details.shopName,
                ...(details.logoUrl && { organizationLogoImageUrl: details.logoUrl }),
                organizationAppTitle: details.shopName,

                address: ObjectUtils.cleanupEmptyValues(details.address),
                configuration: ObjectUtils.cleanupEmptyValues({
                    baseCountryISO2Code: details.countryCode,
                    baseCountryCurrencyCode: details.currency,

                    cardConfiguration: {
                        allowManualCardGeneration: true,
                        loyaltyCardNumberLength: 1
                    },
                    notificationConfiguration: {
                        emailConfiguration: ObjectUtils.cleanupEmptyValues({
                            fromAddress: details.shopEmail
                        }),
                        smsConfiguration: {
                            phoneNumber: "string"
                        }
                    },
                    providerConfiguration: {
                        emailProvidersList: [],
                        smsProvidersList: []
                    },
                    tierConfiguration: {
                        tierCalculationWindow: 365,
                        jobEnabled: false
                    },
                    reportConfiguration: {
                        financialYearStartMonth: 1
                    },
                    portalConfiguration: {
                        allowedOrigins: ["string"],
                        idpMetadata: {
                            realm: "string",
                            clientId: "string",
                            clientSecret: "string",
                            certUrl: "string",
                            issuer: "string",
                            audience: "string"
                        }
                    },
                    memberPrimaryAttribute: primaryAttribute,

                }),

                defaultRegion: ObjectUtils.cleanupEmptyValues({
                    regionName: details.country,
                    defaultCountryISO2Code: details.countryCode,
                    defaultCurrencyCode: details.currency,
                    regionIconUrl: details.countryCode ? `https://flagcdn.com/w80/${details.countryCode.toLowerCase()}.png` : null,

                    defaultMerchant: {
                        name: details.shopName
                    },
                    defaultMerchantLocation: {
                        name: "default"
                    },
                    pointConfiguration: {
                        minPointRedemptionAmount: 1,
                        maxPointRedemptionAmount: 100000,
                        minPointsBalanceForRedemption: 1,
                        pointExpiryMethod: "ROLLING",
                        pointExpiryStartMonth: 0,
                        pointExpiryEndMonth: 0,
                        pointExpiryPeriod: 0,
                        pointExpiryGracePeriod: 0,
                        currencyAmountPerPoint: pointsPerCurrency,
                        jobEnabled: false,
                        regionalPointConversionRates: []
                    },
                    memberConfiguration: {
                        maxSecondaryAccounts: 0
                    },
                    notificationConfiguration: {
                        emailConfiguration: {
                            fromAddress: customerEmail
                        },
                        smsConfiguration: {
                            phoneNumber: "string"
                        }
                    },
                    providerConfiguration: {
                        emailProvidersList: [],
                        smsProvidersList: []
                    },
                    supportInfo: ObjectUtils.cleanupEmptyValues({
                        email: details.shopEmail ?? customerEmail,
                        phoneNumbers: details.mobileNumber ? [details.mobileNumber] : ["unknown"],
                        whatsappNumber: "unknown"
                    }),
                    timeZone: details.timezone,
                }),

            }
            const organizationResponse = await ShoutoutLoyaltyService.createOrganization(organizationCreateRequest, userToken);

            return {
                response: organizationResponse._id,
                organizationCreateStatus: {

                    organizationId: organizationResponse._id,
                    regionId: organizationResponse.configuration.baseRegionId,

                }
            };
        } catch (error) {
            console.error(error, "Failed to create organization");
            return {
                error: new ApiException("Failed to create organization", 500, (error as Error).message),
                organizationCreateStatus: {
                    organizationId: '',
                    regionId: ''
                }
            };
        }
    }

    public static async getRegion(regionId: string, userToken: string): Promise<ReturnType<any>> {
        try {
            const region = await ShoutoutLoyaltyService.getRegionById(regionId, userToken);
            if (region) {
                return {
                    response: region,
                    organizationCreateStatus: {
                        regionId: region._id,
                        merchantId: region.defaultMerchantId,
                        merchantLocationId: region.defaultMerchantLocationId
                    }
                };
            }
            throw new ApiException("Region not found", 404);
        } catch (error) {
            console.error(error, "Failed to get region");
            return {
                error: new Error('Failed to get region'),
                organizationCreateStatus: {}
            };
        }
    }

    public static async createSubTransactionType(userToken: string, shop: string): Promise<ReturnType<string>> {
        try {
            const response = await ShoutoutLoyaltyService.createSubTransactionType({
                transactionType: "REDEMPTION",
                operationType: "SUBTRACT",
                name: "Redeem Points Shopify",
                description: "Redeem Points from Shopify",
                referenceId: `${shop}_redeem_points_shopify`,
            isVisibleToUser: true
        }, userToken);
     
        return {
            response: response._id,
            organizationCreateStatus: {
                redeemPointsSubTransactionTypeId: response._id
            }
        };
    } catch (error) {
        console.error(error, "Failed to create sub transaction type");
        return {
            error: new Error('Failed to create sub transaction type'),
            organizationCreateStatus: {}
        };
    }
    }
    public static async syncBranches(userToken: string, shopifyLocations: any[]): Promise<ReturnType<any>> {
        // Map Shopify locations to your branch format
        const branches = shopifyLocations.map(location => ({
            name: location.name,
            address: location.address1,
            city: location.city,
            province: location.province,
            zip: location.zip,
            country: location.country_name,
            // ... any other fields you need
        }));

        return {
            response: branches,
            organizationCreateStatus: {}
        };
        // Your existing sync logic here
    }


    public static async createPointRule(userToken: string, shop: string, data: ICreatePointRule): Promise<ReturnType<any>> {
        const response = await ShoutoutLoyaltyService.createPointRule(data, userToken);
        return {
            response: response,
            organizationCreateStatus: {
                pointRuleId: response._id
            }
        };
    }
}

export default AppOnboardingHandler;