import ShopifySqsWorker from './workers/shopify.webhook.requests.sqs.job'


export async function appInitializationTask() {
  await initializeSqsConsumers();
}
  
async function initializeSqsConsumers() {
  try {
    // Initialize your SQS consumer here
    const worker = new ShopifySqsWorker();
    await worker.startProcessing();
    console.log("SQS Consumer started successfully");
  } catch (error) {
    console.error("Failed to initialize SQS Consumer:", error);
  }
}
