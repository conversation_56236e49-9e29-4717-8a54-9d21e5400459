import {json} from "@remix-run/node";
import {SHOPIFY_DISCOUNT_FUNCTION_ID} from "app/constants";
import {PriceRule, PriceRuleResponse, ShopifyCustomer} from "../types/shopify.customer";
import {sessionStorage} from "../shopify.server";

class ShopifyAdminApiService {

  private static async getAccessToken(shop: string) {
    const sessions = await sessionStorage?.findSessionsByShop(shop);
    if (!sessions || sessions.length === 0) {
      throw new Error(`No session found for shop ${shop}`);
    }
    const session = sessions[0];
    return session?.accessToken
  }

  public static async createDiscountCode(points: number, cardNumber: string, admin: any, code: string, shopDomain: string | null) {

    const usageLimit = 1;
    const appliesOncePerCustomer = true;
    const adminAccessToken = await this.getAccessToken(shopDomain as string);
    const query = `#graphql
    mutation CreateCodeDiscount($discount: DiscountCodeAppInput!) {
      discountCreate: discountCodeAppCreate(codeAppDiscount: $discount) {
        codeAppDiscount {
          discountId
        }
        userErrors {
          code
          message
          field
        }
      }
    }`;

    const baseCodeDiscount = {
      functionId: SHOPIFY_DISCOUNT_FUNCTION_ID,
      startsAt: new Date(),
      endsAt: new Date(new Date().setDate(new Date().getDate() + 7)),
      title: `Loyalty Points Discount-${cardNumber}-${points}`,
      code,
      usageLimit,
      appliesOncePerCustomer,
      "combinesWith": {
        "orderDiscounts": true,
        "productDiscounts":true,
        "shippingDiscounts": true
      },
      customerSelection: {
        customers: {
          add: [`gid://shopify/Customer/${cardNumber}`]
        }
      }
    };

    const metafields = [{
      namespace: "$app:volume-discount",
      key: "function-configuration",
      type: "json",
      value: JSON.stringify({
        quantity: Number(points.toFixed(2)),
      }),
    }];

    if (!admin && shopDomain) {
      return await fetch(
        `https://${shopDomain}/admin/api/2024-07/graphql.json`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': `${adminAccessToken}`,
          },
          body: JSON.stringify({
            query: query,
            variables: {
              discount: {
                ...baseCodeDiscount,
                metafields,
              },
            },
          })
        }
      );
    }

    return await admin.graphql(
      query,
      {
        variables: {
          discount: {
            ...baseCodeDiscount,
            metafields,
          },
        },
      },
    );
  }

  public static async createFreeShippingDiscountCode(admin: any, discountCode: string, cardNumber: string) {

    if (!admin) {
      return json({error: 'Admin API not available'}, {status: 500});
    }

    return await admin.graphql(
        `#graphql
      mutation discountCodeFreeShippingCreate($freeShippingCodeDiscount: DiscountCodeFreeShippingInput!) {
        discountCodeFreeShippingCreate(freeShippingCodeDiscount: $freeShippingCodeDiscount) {
          codeDiscountNode {
            id
            codeDiscount {
              ... on DiscountCodeFreeShipping {
                title
                startsAt
                endsAt
                maximumShippingPrice {
                  amount
                }
                customerSelection {
                  ... on DiscountCustomers {
                    customers {
                      id
                    }
                  }
                }
                destinationSelection {
                  ... on DiscountCountryAll {
                    allCountries
                  }
                }
                minimumRequirement {
                  ... on DiscountMinimumSubtotal {
                    greaterThanOrEqualToSubtotal {
                      amount
                    }
                  }
                }
                codes(first: 2) {
                  nodes {
                    code
                  }
                }
              }
            }
          }
          userErrors {
            field
            code
            message
          }
        }
      }`,
      {
        variables: {
          "freeShippingCodeDiscount": {
            startsAt: new Date().toISOString(),
            endsAt: new Date(new Date().setHours(new Date().getHours() + 24)).toISOString(),
            title: 'FREE_SHIPPING_DISCOUNT',
            "code": discountCode,
            maximumShippingPrice: null,
            appliesOncePerCustomer: true,
            "combinesWith": {
              "orderDiscounts": true,
              "productDiscounts": false,
              "shippingDiscounts": false
            },
            usageLimit: 1,
            minimumRequirement: {
              "subtotal": {
                "greaterThanOrEqualToSubtotal": 1
              }
            },
            customerSelection: {
              customers: {
                add: [`gid://shopify/Customer/${cardNumber}`]
              }
            },
            "destination": {
              "all": true
            }
          }
        },
      },
    );
  }

  public static async ListCustomerDiscounts(admin: any, customerId: string) {

    if (!admin) {
      return json({error: 'Admin API not available'}, {status: 500});
    }
    return await admin.graphql(
        `#graphql
      query ListDiscounts($query: String) {
        discountNodes(first: 100, query: $query, sortKey: CREATED_AT, reverse: true) {
          nodes {
            id
            metafields(first: 1, namespace: "$app:volume-discount") {
              nodes {
                key
                value
              }
            }
            discount {
              ... on DiscountCodeApp {
                title
                codes(first: 1) {
                  nodes {
                    code
                  }
                }
                status
                usageLimit
                appliesOncePerCustomer
                startsAt
                endsAt
                asyncUsageCount
                hasTimelineComment
                customerSelection {
                  ... on DiscountCustomers {
                    customers {
                      id
                    }
                  }
                }
              }
              ... on DiscountCodeFreeShipping {
                title
                codes(first: 1) {
                  nodes {
                    code
                  }
                }
                status
                usageLimit
                appliesOncePerCustomer
                startsAt
                endsAt
                asyncUsageCount
                customerSelection {
                  ... on DiscountCustomers {
                    customers {
                      id
                    }
                  }
                }
                minimumRequirement {
                  ... on DiscountMinimumSubtotal {
                    greaterThanOrEqualToSubtotal {
                      amount
                    }
                  }
                }
                destinationSelection {
                  ... on DiscountCountryAll {
                    allCountries
                  }
                }
              }
            }
          }
        }
      }`,
      {
        variables: {
          query: `customer_id:'${customerId}'`,
        },
      }
    );

  }

  public static async updateCustomerByGraphQLAdmin(admin: any, customerId: string, customerData: Partial<ShopifyCustomer>) {
    if (!admin) {
      return json({error: 'Admin API not available'}, {status: 500});
    }

    return await admin.graphql(
        `#graphql
      mutation updateCustomerMetafields($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
            metafields(first: 3) {
              edges {
                node {
                  id
                  namespace
                  key
                  value
                }
              }
            }
          }
          userErrors {
            message
            field
          }
        }
      }`,
      {
        variables: {
          "input": {
            ...customerData,
            "id": `gid://shopify/Customer/${customerId}`
          }
        },
      },
    );
  }

  public static async updateCustomerByAPI(shopDomain: string, customerId: string, customerData: Partial<ShopifyCustomer>) {
    const adminAccessToken = await this.getAccessToken(shopDomain as string);
    return await fetch(
      `https://${shopDomain}/admin/api/2025-01/customers/${customerId}.json`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': `${adminAccessToken}`,
        },
        body: JSON.stringify({
          customer: {
            id: customerId,
            ...customerData
          }
        })
      }
    );

  }

  public static async createPriceRuleByAPI(shopDomain: string, priceRule: PriceRule): Promise<PriceRuleResponse> {
    const adminAccessToken = await this.getAccessToken(shopDomain as string);
    const response = await fetch(
      `https://${shopDomain}/admin/api/2025-04/price_rules.json`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': `${adminAccessToken}`,
        },
        body: JSON.stringify({
          price_rule: priceRule,
        }),
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to create price rule: ${response.statusText}`);
    }
    return response.json();
  }

  public static async createDiscountCodeByAPI(shopDomain: string, priceRuleId: string, code: string) {
    const adminAccessToken = await this.getAccessToken(shopDomain as string);
    const response = await fetch(
      `https://${shopDomain}/admin/api/2025-04/price_rules/${priceRuleId}/discount_codes.json`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': `${adminAccessToken}`,
        },
        body: JSON.stringify({
          discount_code: {
            code
          }
        })
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to create discount code: ${response.statusText}`);
    }
    return response.json();
  }
}

export default ShopifyAdminApiService;
