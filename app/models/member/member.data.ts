import { Address } from './address.data';
import { NotificationPreference } from './notificationPreference.data';

export class Member {
    regionId: string;
    cardNumber: string;
    autoGenerateCard: boolean;
    merchantLocationCode: string;
    firstName: string;
    lastName: string;
    preferredName: string;
    mobileNumber: string;
    email: string;
    gender?: string; 
    residentialAddress: Address | null;
    postalAddress: Address | null;
    customAttributes: Record<string, any>; // For flexible key-value pairs
    notificationPreference: NotificationPreference | null;
  
    constructor(
      regionId: string,
      cardNumber: string,
      autoGenerateCard: boolean,
      merchantLocationCode: string,
      firstName: string,
      lastName: string,
      preferredName: string,
      mobileNumber: string,
      email: string,
      residentialAddress: Address | null,
      postalAddress: Address | null,
      customAttributes: Record<string, any>,
      notificationPreference: NotificationPreference | null,
      gender?: string,
    ) {
      this.regionId = regionId;
      this.cardNumber = cardNumber;
      this.autoGenerateCard = autoGenerateCard;
      this.merchantLocationCode = merchantLocationCode;
      this.firstName = firstName;
      this.lastName = lastName;
      this.preferredName = preferredName;
      this.mobileNumber = mobileNumber;
      this.email = email;
      this.gender = gender;
      this.residentialAddress = residentialAddress;
      this.postalAddress = postalAddress;
      this.customAttributes = customAttributes;
      this.notificationPreference = notificationPreference;
    }
  }