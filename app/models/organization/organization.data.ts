export type OrganizationCreateData = {

    organizationName: string,
    organizationLogoImageUrl?: string,
    organizationFavicon?: string,
    organizationAppTitle?: string,
    address?: {
        line1: string,
        line2?: string,
        line3?: string,
        city: string,
        stateOrProvince: string,
        zipOrPostcode: string
    },
    configuration: {
        baseCountryISO2Code: string,
        baseCountryCurrencyCode: string,
        cardConfiguration: {
            loyaltyCardNumberLength?: number,
            allowManualCardGeneration?: boolean
        },
        notificationConfiguration?: {
            emailConfiguration?: {
                fromAddress: string
            },
            smsConfiguration?: {
                phoneNumber: string
            }
        },


        memberPrimaryAttribute: string,

    },
    defaultRegion: {
        regionName: string,
        defaultCountryISO2Code: string,
        defaultCurrencyCode: string,
        regionIconUrl?: string,

        defaultMerchant: {
            name: string
        },
        defaultMerchantLocation: {
            name: string
        },
        pointConfiguration: {
            minPointRedemptionAmount: number,
            maxPointRedemptionAmount: number,
            minPointsBalanceForRedemption: number,
            pointExpiryMethod: "ROLLING" | "FIXED",
            pointExpiryStartMonth: number,
            pointExpiryEndMonth: number,
            pointExpiryPeriod: number,
            pointExpiryGracePeriod: number,
            currencyAmountPerPoint: number,
            jobEnabled: boolean,
            regionalPointConversionRates: {
                destinationRegionId: string,
                rate: number
            }[]
        },
        memberConfiguration: {
            maxSecondaryAccounts: number
        },
        notificationConfiguration: {
            emailConfiguration: {
                fromAddress: string
            },
            smsConfiguration: {
                phoneNumber: string
            }
        },
        providerConfiguration: {
            emailProvidersList: [],
            smsProvidersList: []
        },
        supportInfo: {
            phoneNumbers: [
                string
            ],
            email: string,
            whatsappNumber: string
        },
        timeZone: string
    }

}