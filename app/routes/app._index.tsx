import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import {
  Page,
  Layout,
  Text,
  Card,
  BlockStack,
  InlineStack,
  Link,
} from "@shopify/polaris";

import { authenticate } from "../shopify.server";

import { SHOUTOUT_ADMIN_PORTAL_URL } from "app/constants";
import OrganizationDao from "app/db/organization.dao";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session, redirect } = await authenticate.admin(request);
  console.debug("session", session);
  const organization = await OrganizationDao.getOrganization(session.shop);

  if (!organization) {
    return redirect("/app/onboarding");
  }

  return json({ organization });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session, redirect } = await authenticate.admin(request);
  // ... rest of your action code ...
  return redirect("/app");
};

export default function Index() {
  return (
    <Page>
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <InlineStack gap="300">
                  <Text as="p" variant="bodyMd">
                    Welcome to ShoutOUT loyalty shopify app.
                  </Text>


                </InlineStack>
              </BlockStack>
              <BlockStack gap="300">
                <Link url={SHOUTOUT_ADMIN_PORTAL_URL} target="_blank">
                  Go to Admin Portal
                </Link>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
