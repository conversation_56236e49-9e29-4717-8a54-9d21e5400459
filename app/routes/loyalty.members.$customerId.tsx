import type {LoaderFunctionArgs} from "@remix-run/node";
import {authenticate} from "../shopify.server";
import {json} from "@remix-run/node";
import OrganizationDao from "../db/organization.dao";
import ShoutoutLoyaltyService from "../services/shoutout.loyalty.service.v2";
import type {Params} from "@remix-run/react";
import type {Organization} from "@prisma/client";
import {generateRandomCode} from "../utils/code.utils";
import ShopifyAdminApiService from "../services/shopify.adminapi.service";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "86400" // 24 hours
};

const authRequest = async ({request, params}: { request: Request, params: Params }): Promise<{
  shopDomain: string,
  customerId: string,
  organization: Organization,
  admin: any
}> => {
  const {admin, session} = await authenticate.admin(request);
  const shopDomain = session?.shop;

  if (!shopDomain) {
    throw new Error("Shop domain not found");
  }

  const {customerId} = params;
  if (!shopDomain) {
    throw new Error('Shop domain not found');
  }
  if (!customerId) {
    throw new Error('Customer ID not found');
  }
  const organization = await OrganizationDao.getOrganization(shopDomain);

  if (!organization) {
    throw new Error('Organization not found');
  }
  return {shopDomain, customerId, organization, admin}
}

export const loader = async ({request, params}: LoaderFunctionArgs) => {
  console.log("loyalty member request:", request.url);
  try {
    if (request.method === "OPTIONS") {
      return new Response(null, {headers: corsHeaders});
    }
    console.log("Request headers:", Object.fromEntries(request.headers.entries()));
    const {organization, customerId, shopDomain, admin} = await authRequest({request, params});
    const url = new URL(request.url);
    const generateFreeShippingDiscountCode = url.searchParams.get('generateFreeShippingDiscountCode') === 'true';

    const members = await ShoutoutLoyaltyService.getMember(shopDomain, organization.regionId, 'CARD_NUMBER', customerId);

    if (members?.items?.length === 0) {
      return json({error: 'Member not found'}, {status: 404, headers: corsHeaders});
    }

    if (generateFreeShippingDiscountCode && organization?.configuration?.freeShippingEligibleTiers?.includes(members?.items[0]?.tier?.tierId)) {
      const code = generateRandomCode(8);
      const discountResponse = await ShopifyAdminApiService.createFreeShippingDiscountCode(
        admin,
        code,
        customerId
      );
      let responseJson = await discountResponse.json();
      responseJson = responseJson.data;
      const errors = responseJson?.discountCodeFreeShippingCreate?.userErrors;
      if (errors?.length !== 0) {
        return json({error: errors}, {status: 400, headers: corsHeaders});
      }
      console.log('members', members?.items[0]);
      return json({
        member: members?.items[0],
        discountCode: code,
        availableFreeShippingDiscountCode: true
      }, {headers: corsHeaders});
    }

    console.log('members', members?.items[0]);
    return json({
      member: members?.items[0],
      availableFreeShippingDiscountCode: false
    }, {headers: corsHeaders});

  } catch (error: unknown) {
    console.error("Error in checkout API:", error instanceof Error ? error.message : error);
    if (error && typeof error === 'object' && 'body' in error) {
      console.error("Error body:", (error as { body: unknown }).body);
    }
    return json({error: "Internal server error"}, {status: 500, headers: corsHeaders});
  }

};

