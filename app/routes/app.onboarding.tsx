import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import {
  Page,
  Layout,
  Card,
  Form,
  FormLayout,
  TextField,
  Button,
  Text,
  Select
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";

import { useActionData, useSubmit, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";

import ApiException from "app/utils/api.exception";

import AppOnboardingHandler from "app/handlers/app.onboarding.handler";
import OrganizationDao from "app/db/organization.dao";
import OrganizationCreateStatusDao from "app/db/organization.create.status.dao";
import { OrganizationCreateStatus } from "@prisma/client";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  // Get the shop owner's email
  // const { shop, accessToken } = session;
  const response = await admin.rest.get({
    path: '/shop.json',
  });
  const shopData = await response.json();


  // Check if organization exists...
  const organization = await OrganizationDao.getOrganization(session.shop);

  if (organization) {
    return redirect("/app");
  }

  const { id, shop_owner, phone, city, province, address1, zip, address2, country_name, country_code, currency, name, email,
    customer_email, timezone, iana_timezone, domain, myshopify_domain } = shopData.shop;
  const [firstName = "", lastName = ""] = shop_owner?.split(' ') || [];

  // Construct the shop logo URL
  const shopDomain = domain || myshopify_domain;
  let logoUrl: string | null = `https://${shopDomain}/cdn/shop/files/logo.png`;  // default path

  // You can also try to fetch the logo to verify it exists
  try {
    const logoResponse = await fetch(logoUrl);
    if (!logoResponse.ok) {
      // If logo doesn't exist at default path, set to null or a default logo
      logoUrl = null;
    }
  } catch (error) {
    logoUrl = null;
  }

  const details = {
    id,
    firstName: firstName,
    lastName: lastName,
    mobileNumber: phone,
    shopEmail: email,
    customerEmail: customer_email,
    currency: currency,
    timezone: iana_timezone ?? timezone,
    address: {
      city: city ?? '-',
      stateOrProvince: province ?? '-',
      line1: address1 ?? '-',
      line2: address2 ?? '-',
      zipOrPostcode: zip ?? '-',

    },
    country: country_name ?? '-',
    countryCode: country_code ?? '',
    shopName: name,
    logoUrl: logoUrl
  }
  return json({ details });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session, admin } = await authenticate.admin(request);

  const formData = await request.formData();
  const details = JSON.parse(formData.get("shopDetails") as string);
  let customerEmail = formData.get("email") as string;
  let password = formData.get("password") as string;
  const primaryAttribute = formData.get("primaryAttribute") as string;
  const pointsPerCurrency = Number(formData.get("pointsPerCurrency")) || 1;
  const pointValue = Number(formData.get("pointValue")) || 0.1;
  
  let organizationCreateStatus: Partial<Omit<OrganizationCreateStatus, 'id' | 'createdAt' | 'updatedAt'>> = {
    shop: session.shop,
    rootUserId: '',
    organizationId: '',
    regionId: '',
    organizationCreated: false,
    redeemPointsSubTransactionTypeId: '',
    pointRuleId: ''
  };
  try {
    const existingOrganizationCreateStatus = await OrganizationCreateStatusDao.getOrganizationCreateStatus(session.shop);
    if (existingOrganizationCreateStatus) {
      organizationCreateStatus = { ...organizationCreateStatus, ...existingOrganizationCreateStatus };
      if (existingOrganizationCreateStatus.email) {
        customerEmail = existingOrganizationCreateStatus.email;
      }
      if (existingOrganizationCreateStatus.password) {
        password = existingOrganizationCreateStatus.password;
      }
    }

    if (!organizationCreateStatus.rootUserId) {

      const signupResponse = await AppOnboardingHandler.signupUser(customerEmail, password, details);
      if (signupResponse.error || !signupResponse.response) {
        throw new ApiException("Failed to signup user", 500, signupResponse.error?.message);
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...signupResponse.organizationCreateStatus
      }

    }


    const userTokenResponse = await AppOnboardingHandler.getUserToken(customerEmail, password);
    if (userTokenResponse.error || !userTokenResponse.response) {
      throw new ApiException("Failed to retrieve user token", 500, userTokenResponse.error?.message);
    }


    // try {
    if (!organizationCreateStatus.organizationCreated) {
      const organizationResponse = await AppOnboardingHandler.createOrganization(details, customerEmail, primaryAttribute, pointValue, userTokenResponse.response);
      if (organizationResponse.error || !organizationResponse.response) {
        throw organizationResponse.error || new Error("Failed to create organization");
      }

      organizationCreateStatus = {
        ...organizationCreateStatus,
        organizationCreated: true,
        ...organizationResponse.organizationCreateStatus
      }
    }



    if (!organizationCreateStatus.permissionGroupId) {
      const permissionGroupResponse = await AppOnboardingHandler.createPermissionGroup(userTokenResponse.response);
      if (permissionGroupResponse.error || !permissionGroupResponse.response) {
        throw new ApiException("Failed to create permission group", 500, permissionGroupResponse.error?.message);
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...permissionGroupResponse.organizationCreateStatus
      }
    }

    if (!organizationCreateStatus.integrationId) {
      const integrationClientResponse = await AppOnboardingHandler.createIntegrationClient(organizationCreateStatus.organizationId ?? '', userTokenResponse.response);

      if (integrationClientResponse.error) {
        throw new ApiException("Failed to create integration client", 500, integrationClientResponse.error.message);
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...integrationClientResponse.organizationCreateStatus
      }

    }

    if (!organizationCreateStatus.assignedPermissions) {
      const assignPermissionsResponse = await AppOnboardingHandler.assignPermissions(organizationCreateStatus.integrationId ?? '', organizationCreateStatus.permissionGroupId ?? '', organizationCreateStatus.regionId ?? '', userTokenResponse.response);
      if (assignPermissionsResponse.error) {
        throw new ApiException("Failed to assign permissions", 500, assignPermissionsResponse.error.message);
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...assignPermissionsResponse.organizationCreateStatus
      }
    }


    if (!organizationCreateStatus.merchantId) {
      const regionResponse = await AppOnboardingHandler.getRegion(organizationCreateStatus.regionId ?? '', userTokenResponse.response);
      if (regionResponse.error || !regionResponse.response) {
        throw regionResponse.error || new Error("Failed to get region");
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...regionResponse.organizationCreateStatus
      }
    }

    if (!organizationCreateStatus.redeemPointsSubTransactionTypeId) {
      const subTransactionTypeResponse = await AppOnboardingHandler.createSubTransactionType(userTokenResponse.response, session.shop);
      if (subTransactionTypeResponse.error || !subTransactionTypeResponse.response) {
        throw subTransactionTypeResponse.error || new Error("Failed to create sub transaction type");
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...subTransactionTypeResponse.organizationCreateStatus
      }
    }

    //TODO: sync shopify branches with shoutout branches
    // First get Shopify locations
    // const locationsResponse = await admin.rest.get({
    //   path: '/locations.json',
    // });
    // const shopifyLocations = await locationsResponse.json();

    // await AppOnboardingHandler.syncBranches(
    //   userTokenResponse.response, 
    //   shopifyLocations.locations // Pass the locations to your sync method
    // );

    if (!organizationCreateStatus.pointRuleId) {
      const pointRuleResponse = await AppOnboardingHandler.createPointRule(userTokenResponse.response, session.shop, {
        regionId: organizationCreateStatus.regionId ?? '',
        merchantId: organizationCreateStatus.merchantId ?? '',
        name: 'general spending',
        description: 'auto generated from shopify',
        type: 'TRANSACTIONAL',
        subType: 'GENERAL',
        maxPoints: null,
        ruleData: { amountPerPoint: pointsPerCurrency },
        status: 'ENABLED'
      });
      if (pointRuleResponse.error || !pointRuleResponse.response) {
        throw new ApiException("Failed to create point rule", 500, pointRuleResponse.error?.message);
      }
      organizationCreateStatus = {
        ...organizationCreateStatus,
        ...pointRuleResponse.organizationCreateStatus
      }
    }

    await OrganizationDao.createOrganization({
      shop: session.shop,
      organizationId: organizationCreateStatus.organizationId ?? '',
      regionId: organizationCreateStatus.regionId ?? '',
      userId: organizationCreateStatus.rootUserId ?? '',
      clientId: organizationCreateStatus.integrationClientId ?? '',
      clientSecret: organizationCreateStatus.integrationClientSecret ?? '',
      merchantId: organizationCreateStatus.merchantId ?? '',
      merchantLocationId: organizationCreateStatus.merchantLocationId ?? '',
      redeemPointsSubTransactionTypeId: organizationCreateStatus.redeemPointsSubTransactionTypeId ?? ''
    });

    delete organizationCreateStatus.integrationClientId;
    delete organizationCreateStatus.integrationClientSecret;
    delete organizationCreateStatus.password;


    // } catch (error) {
    //   console.error(error, "Failed to create organization");
    //   organizationCreateStatus = {
    //     ...organizationCreateStatus,
    //     password: '',
    //     userToken: userTokenResponse.response,
    //   }
    //   throw new ApiException("Failed to create organization", 500, (error as Error).message);
    // }




    return redirect("/app");
  } catch (error) {
    return json({
      error: error instanceof Error ? error.message : "Failed to create account"
    }, { status: 400 });
  } finally {
    try {
      await OrganizationCreateStatusDao.upsertOrganizationCreateStatus({
        shop: session.shop,
        ...organizationCreateStatus
      });

    } catch (error) {
      // Handle error if needed
      console.error(error, "Failed to update organization create status");
    }
  }
};

export default function OnboardingPage() {
  const submit = useSubmit();
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  const [password, setPassword] = useState("");
  const [primaryAttribute, setPrimaryAttribute] = useState("EMAIL");
  const [pointsPerCurrency, setPointsPerCurrency] = useState("1");
  const [email, setEmail] = useState(loaderData.details.customerEmail ?? loaderData.details.shopEmail);
  const [pointValue, setPointValue] = useState("0.1");

  // Add navigation to check loading state
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <Page
      title="Welcome to ShoutOUT Loyalty"
      subtitle="Provide an email and a password to create an account"
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Form method="post" onSubmit={(e) => {
              e.preventDefault();
              const form = new FormData(e.currentTarget);
              // Add the shop details to the form data
              form.append("shopDetails", JSON.stringify(loaderData.details));
              submit(form, { method: "post" });
            }}>
              <FormLayout>
                <TextField
                  label="Email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={setEmail}
                  requiredIndicator={true}
                  autoComplete="email"
                  readOnly={false}
                />
                <Select
                  label="What's the primary attribute for your customers?"
                  name="primaryAttribute"
                  options={[
                    { label: 'Email', value: 'EMAIL' },
                    { label: 'Mobile Number', value: 'MOBILE_NUMBER' }
                  ]}
                  value={primaryAttribute}
                  onChange={setPrimaryAttribute}
                  requiredIndicator={true}
                  helpText="This is the attribute that will be used to identify your customers in the loyalty program"
                />

                <TextField
                  label="How much should be the bill amount for a point?"
                  name="pointsPerCurrency"
                  type="number"
                  value={pointsPerCurrency}
                  onChange={setPointsPerCurrency}
                  requiredIndicator={true}
                  helpText="For example, if you enter 10, customers will earn 1 point for every 10 dollars spent"
                  autoComplete="off"
                />

                <TextField
                  label="What is the value of one point in currency?"
                  name="pointValue"
                  type="number"
                  value={pointValue}
                  onChange={setPointValue}
                  requiredIndicator={true}
                  helpText="For example, if you enter 0.1, customers will get 1 dollar discount for every 10 points when redeemed"
                  autoComplete="off"
                />

                <TextField
                  label="Password for your loyalty account (This is not your Shopify password)"
                  name="password"
                  type="password"
                  value={password}
                  onChange={setPassword}
                  requiredIndicator={true}
                  autoComplete="new-password"
                  helpText="This password will be used to login to your loyalty account"
                />
                {actionData?.error && (
                  <Text as="p" tone="critical">{actionData.error}</Text>
                )}
                <Button
                  submit
                  variant="primary"
                  disabled={!password || isSubmitting}
                  loading={isSubmitting}
                >
                  {isSubmitting ? "Creating Account..." : "Create Loyalty Account"}
                </Button>
              </FormLayout>
            </Form>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
} 