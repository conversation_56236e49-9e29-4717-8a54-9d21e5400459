import { Consumer, ConsumerOptions } from 'sqs-consumer';
import { Message, SQSClient } from '@aws-sdk/client-sqs';
import WorkerBaseClass from './worker.base.class';
import { AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY } from './../constants';


abstract class AbstractSqsConsumer<T> extends WorkerBaseClass<T> {
    protected consumer: any;
    protected logger: any;

    constructor(queueOptions: ConsumerOptions, logger: any) {
        super();
        this.logger = logger;
        // if (!AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY) {
        //     throw new Error("AWS credentials are missing. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY.");
        // }
        const sqsClient = new SQSClient({
            region: AWS_REGION,
            // credentials: {
            //     accessKeyId: AWS_ACCESS_KEY_ID,
            //     secretAccessKey: AWS_SECRET_ACCESS_KEY,
            // },
        });

        this.consumer = Consumer.create({
            ...queueOptions,
            sqs: sqsClient,
            handleMessage: async (message: Message): Promise<Message> => {
                try {
                    await this._handleMessage(message);
                    return message;
                } catch (error) {
                    this.logger.error('Error processing message:', error);
                    return Promise.reject(error);
                }
            },
            handleMessageBatch: async (messages: Message[]): Promise<Message[]> => {
                try {
                    const results = await Promise.allSettled(messages.map(async (message: Message) => {
                        return this._handleMessage(message);
                    }));

                    return results.reduce((acc: Message[], result: any, index: number) => {
                        if (result.status === 'fulfilled') {
                            this.logger.info(`Message processed successfully: ${result.value}`);
                            acc.push(messages[index]);
                        } else {
                            this.logger.error(`Error processing message: ${result.reason}`);
                        }
                        return acc;
                    }, []);
                } catch (error) {
                    this.logger.error('Error processing messages:', error);
                    return Promise.reject(error);
                }
            },
        });
    }

    async _handleMessage(message: Message): Promise<string> {
        if (message.Body) {
            return await this.processTask(JSON.parse(message.Body) as T, message.MessageId ?? "");
        }
        throw new Error("Message body is empty.");
    }
    startConsuming(): void {
        this.consumer.start();
    }

    stopConsuming(): void {
        this.consumer.stop();
    }
}

export default AbstractSqsConsumer;
