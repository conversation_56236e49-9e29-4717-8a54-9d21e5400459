import AbstractSqsConsumer from './sqs.worker.base.class';

import {
    SQS_QUEUE_URL,
    AWS_REGION
} from './../constants';
import organizationDao from 'app/db/organization.dao';
import type AbstractShopifyWebhookHandler from './abstract.shopify.webhook.handler';
import ShopifyOrdersCreateHandler from './webhooks/shopify.orders.create';
import ShopifyCustomersCreateHandler from './webhooks/shopify.customers.create';
import type { ShopifyWebhookRequest } from 'app/types/shopify.workers';
import logger from './../logger';

class ShopifySqsWorker extends AbstractSqsConsumer<ShopifyWebhookRequest> {

    private webhookRegistry = new Map<string, AbstractShopifyWebhookHandler<ShopifyWebhookRequest>>([
        ["orders/create", new ShopifyOrdersCreateHandler()],
        ["customers/create", new ShopifyCustomersCreateHandler()]
    ]);
    constructor() {
        if (!SQS_QUEUE_URL || !AWS_REGION) {
            throw new Error("SQS_QUEUE_URL and AWS_REGION must be defined");
        }
        super(
            {
                queueUrl: SQS_QUEUE_URL,
                handleMessageTimeout: 120000,
                visibilityTimeout: 150,
                region: AWS_REGION
            },
            logger
        );
    }

    // Process a single SQS message
    protected async processTask(job: ShopifyWebhookRequest, messageId: string): Promise<string> {

        try {
            const { shopify_shop: shopifyShopDomain, shopify_topic: shopifyTopic } = job.metadata;

            // const shopifyShop = shopifyShopDomain.split(".")[0];
            // // const messageType = job?.admin_graphql_api_id || null;
            // if (!shopifyShop) {
            //     throw new Error(`Shopify shop domain is missing: ${shopifyShopDomain}`);
            // }

            const organization = await organizationDao.getOrganization(shopifyShopDomain);
            if (!organization) {
                throw new Error(`Organization not found for shop: ${shopifyShopDomain}`);
            }

            const handler = this.webhookRegistry.get(shopifyTopic);
            if (!handler) {
                throw new Error(`Webhook handler not found for topic: ${shopifyTopic}`);
            }
            await handler.process(job, organization);


            // if (!messageType) {
            //     throw new Error("Message type is missing.");
            // }
            // if (!SHOUTOUT_API_CLIENT_ID || !SHOUTOUT_API_CLIENT_SECRET) {
            //     throw new Error("ShoutOUT API credentials are missing. Please set SHOUTOUT_API_CLIENT_ID and SHOUTOUT_API_CLIENT_SECRET.");
            // }
            // const loyaltyService = new ShoutoutLoyaltyService(SHOUTOUT_API_CLIENT_ID, SHOUTOUT_API_CLIENT_SECRET);
            // if (messageType.includes("Order")) {
            //     await this.handleOrder(job, loyaltyService);
            // } else if (messageType.includes("Customer")) {
            //     await this.handleCustomer(job, loyaltyService);
            // } else {
            //     console.log("Unhandled message type.");
            // }

            return Promise.resolve(messageId);
        } catch (error: any) {
            logger.error("Error processing task:", error);
            if(error?.status==='111400'){//duplicate transaction
                return Promise.resolve(messageId);
            }
            throw error;
        }
    }


    // private async handleCustomer(job: any, shopifyShop: string) {
    //     //TODO: Load organization details from the @organizationDao and make this call dynamic. Call loyalty service v2 endpoints
    //     const memberData = initializeMemberData(job);
    //     const response = await loyaltyService.registerLoyaltyMember(memberData);
    //     if (!response.ok) {
    //         throw new Error(`Failed to collect points: ${response.statusText}`);
    //     }
    //     const result = await this.parseResponse(response);
    // }


    // Start processing the SQS queue
    public async startProcessing(): Promise<void> {
        // Logger.log.info('Starting SQS message consumption...');
        this.startConsuming();
    }

    // Stop processing the SQS queue
    public async stopProcessing(): Promise<void> {
        // Logger.log.info('Stopping SQS message consumption...');
        this.stopConsuming();
    }
}

export default ShopifySqsWorker;
