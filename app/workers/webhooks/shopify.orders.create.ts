import type {Organization} from "@prisma/client";
import AbstractShopifyWebhookHandler from "../abstract.shopify.webhook.handler";
import ShoutoutLoyaltyService from "app/services/shoutout.loyalty.service.v2";
import type {ShopifyWebhookRequest} from "app/types/shopify.workers";
import logger from "app/logger";
import type {IProductItem, ITransaction} from "app/types/shoutout.transaction.type";
import {PrimaryKeys} from "../../data";
import {PRIMARY_KEY} from "../../constants";


class ShopifyOrdersCreateHandler extends AbstractShopifyWebhookHandler<ShopifyWebhookRequest> {

  private async _transformOrderData(order: any, organization: Organization): Promise<ITransaction> {
    // const memberPhone = order.phone; // Assuming this is where you get the member phone
    const billAmount = Number(order.current_subtotal_price); // Assuming this is where you get the bill amount
    const transactionDate = new Date(order.created_at); // Convert to Date object
    // DISCUSSION: Using the mobile number as the card number is problematic. What happened if the mobile number is not primary attribute?
    // Validation
    const cardNo = String(order.customer.id);

    if(PRIMARY_KEY===PrimaryKeys.CARD_NUMBER){
      if (!cardNo) {
        throw new Error("Card number is required.");
      }
    }

    const mobileNumber = order?.customer?.phone?.replace("+", "");

    if (!mobileNumber) {
      throw new Error("Mobile number is required.");
    }
    const members = await ShoutoutLoyaltyService.getMember(organization.shop, organization.regionId, PrimaryKeys.MOBILE_NUMBER, mobileNumber);
    if (members?.items?.length === 0) {
      throw new Error("Member not found.");
    }
    const merchantId = organization.merchantId; // TODO : This validation will make sense when the merchant id is loaded form DB
    if (!merchantId) {
      throw new Error("Merchant ID is required.");
    }

    const merchantLocationId = organization.merchantLocationId; // TODO : Merchant Location Code
    if (!merchantLocationId) {
      throw new Error("Merchant Location Id is required.");
    }

    if (billAmount === undefined) {
      throw new Error("Bill amount is required.");
    }

    if (!transactionDate || isNaN(transactionDate.getTime())) {
      throw new Error("Transaction date is required and must be a valid date.");
    }

    const productItems: IProductItem[] = order.line_items.map(({product_id, quantity, title, price}: any) => ({
      productId: String(product_id),
      productName: title,
      quantity,
      amount: price
    }));


    return {
      ...(PRIMARY_KEY===PrimaryKeys.MOBILE_NUMBER? {memberId: members?.items?.[0]?._id}:{}),
      ...(PRIMARY_KEY===PrimaryKeys.CARD_NUMBER? {cardNo}:{}),
      merchantId,
      merchantLocationId,
      billAmount,
      transactionDate,
      idempotentKey: `inc-${order.id}`,
      productItems,
      invoiceData: {
        invoiceId: order.id.toString(),
        invoiceDate: new Date(order.created_at),
        invoiceAmountWithTax: order.current_total_price,
        invoiceAmountWithoutTax: order.current_subtotal_price,
        discountAmount: order.current_total_discounts,
        billAmount: order.current_total_price
      }

    }
  }

  public async process(job: ShopifyWebhookRequest, organization: Organization): Promise<void> {

    logger.info("Processing order create webhook for organization:", organization.id);

    const transactionData = await this._transformOrderData(job.body, organization);
    return await ShoutoutLoyaltyService.updateLoyaltyPointsCollection(organization.shop, transactionData);

  }
}

export default ShopifyOrdersCreateHandler;
