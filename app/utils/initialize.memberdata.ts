import { Member } from '../models/member/member.data';
import { Address } from '../models/member/address.data';
import { NotificationPreference } from '../models/member/notificationPreference.data';

export default function initializeMemberData(data: any): Member {
    // Mandatory fields
    const regionId = "6699296d1be8223ed75ebba4"; // TODO : Take this from DB
    const cardNumber = data.id.toString(); // TODO : This should be changed
    const merchantLocationCode = "1001"; // TODO : Take this from DB
    const firstName = data.first_name;
    const lastName = data.last_name;
    const preferredName = firstName;
    const mobileNumber = "+94323344576"; // TODO  : Change this to get the mobile number
    const email = data.email;

    // Validation
    if (!regionId) {
        throw new Error("Region ID is required.");
    }

    if (!cardNumber || cardNumber.trim() === "") {
        throw new Error("Card number is required.");
    }

    if (!merchantLocationCode) {
        throw new Error("Merchant Location Code is required.");
    }

    if (!mobileNumber) {
        throw new Error("Mobile Number is required.");
    }

    if (!firstName || !lastName || !email) {
        throw new Error("First name, last name, and email are required.");
    }

    // Optional fields with default or fallback values
    const autoGenerateCard = false; // TODo : Need figure out how to get this value

    // Address mapping
    let residentialAddress: Address | null = null; // Initialize as null or any other fallback value

    // Check if all address fields are present
    if (
        data.default_address?.address1 &&
        data.default_address?.address2 &&
        data.default_address?.city &&
        data.default_address?.province &&
        data.default_address?.zip &&
        data.default_address?.country
    ) {
        // Only create the Address object if all fields are present
        residentialAddress = new Address(
            data.default_address.address1,
            data.default_address.address2,
            data.default_address.city,
            data.default_address.province,
            data.default_address.zip,
            data.default_address.country
        );
    }

    // Notification preference
    let notificationPreference: NotificationPreference | null = null;

    // Custom attributes
    const customAttributes = data.customAttributes ?? {};

    // Construct and return the Member object
    return new Member(
        regionId,
        cardNumber,
        autoGenerateCard,
        merchantLocationCode,
        firstName,
        lastName,
        preferredName,
        mobileNumber,
        email,
        residentialAddress,
        residentialAddress,
        customAttributes,
        notificationPreference
    );
}
