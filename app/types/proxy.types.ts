import type {Session} from '@shopify/shopify-api';


interface HandleDiscountCodesParams {
  admin: any;
  customerId: string;
}

interface HandleLoaderDefaultParams {
  shop:string;
  customerId: string;
  primaryKey:string;
  mobileNumber:string;
}

interface AuthPublicProxySuccessResponse {
  admin: any;
  session: Session;
  shop: string;
}

interface AuthPublicCheckoutSuccessResponse {
  customerId: string;
  shop: string;
}

interface HandleRegisterWithOtpRequestParams {
  mobileNumber:string;
  customerId:string;
  shop:string;
  admin: any;
  session: Session;
  primaryKey:string;
}
interface HandleRegisterWithOtpParams {
  registerMemberToken:string;
  otpCode:string;
  shop:string;
}

interface HandleRedeemPointsWithOtpRequestParams {
  pointsAmount:number;
  memberId:string;
  shop:string;
  action:string;
  admin:any;
  customerId:number;
}
interface HandleLoaderDefaultResponse {
  tiers: Array<any>;
  member: null | {};
  freeShippingEligible: boolean;
  pointConfiguration: {
    minPointRedemptionAmount: number;
    maxPointRedemptionAmount: number;
    minPointsBalanceForRedemption: number;
    currencyAmountPerPoint: number;
    pointsEarnedRatio: number;
  };
  generalSpendingRule: {
    subType: string;
    ruleData:
      | {
      pointAmountsForRangesEnabled: true;
      pointAmountMappingsForRanges: Array<any>;
    }
      | {
      pointAmountsForRangesEnabled: false;
      amountPerPoint: number;
    };
  };
}

interface HandleRedeemPointsWithOtpParams {
  redemptionToken:string;
  otpCode:string;
  shop:string;
}

interface HandleGenerateFreeShippingCouponParams {
  shop:string;
  admin:any;
  customerId:number;
  memberId:string;
}

type ReturnType<T> = {
  error?: Error;
  response?: T;
  status: number;
};

export type {
  AuthPublicProxySuccessResponse,
  HandleDiscountCodesParams,
  ReturnType,
  HandleLoaderDefaultParams,
  HandleRegisterWithOtpRequestParams,
  HandleRegisterWithOtpParams,
  HandleRedeemPointsWithOtpRequestParams,
  HandleRedeemPointsWithOtpParams,
  HandleGenerateFreeShippingCouponParams,
  AuthPublicCheckoutSuccessResponse,
  HandleLoaderDefaultResponse
}
