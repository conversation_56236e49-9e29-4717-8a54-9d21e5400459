export interface IProductItem {
    productId: string;
    productName: string;
    productCategory?: [string];
    quantity: number;
    amount: number;
}

export interface IInvoiceData {
    invoiceId: string;
    invoiceDate: Date;
    invoiceAmountWithTax: number;
    invoiceAmountWithoutTax: number;
    discountAmount: number;
    billAmount: number;
}

export interface ITransaction {
    memberId?: string;
    cardNo?: string;
    merchantId: string;
    merchantLocationId: string;
    billAmount: number;
    transactionDate: Date;
    idempotentKey: string;
    productItems?: IProductItem[];
    invoiceData?:IInvoiceData;
}
