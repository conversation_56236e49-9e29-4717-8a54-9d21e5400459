 interface ShopifyCustomerMetafile {
   namespace: string;
   key: string;
   value: string;
   type: string;
}
interface ShopifyCustomer {
  metafields?: ShopifyCustomerMetafile[];
  email?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  tags?: string[] | string;
  note?: string;
  accepts_marketing?: boolean;
  [key: string]: any;
}
 interface PriceRule {
   title: string;
   target_type: string;
   target_selection:  string;
   allocation_method: string;
   value_type: string;
   value: string;
   customer_selection: string;
   prerequisite_customer_ids?: number[];
   usage_limit?: number;
   starts_at: string;
   ends_at?: string;
 }

 interface DiscountCode {
   code: string;
 }

  interface PriceRuleResponse {
    price_rule : {
      id: number;
      value_type:string;
      value: string;
      customer_selection: string;
      target_type: string;
      target_selection: string;
      allocation_method: string;
      allocation_limit: number | null;
      once_per_customer: boolean;
      usage_limit: number | null;
      starts_at: string;
      ends_at: string | null;
      created_at: string;
      updated_at: string;
      entitled_product_ids: number[];
      entitled_variant_ids: number[];
      entitled_collection_ids: number[];
      entitled_country_ids: number[];
      prerequisite_product_ids: number[];
      prerequisite_variant_ids: number[];
      prerequisite_collection_ids: number[];
      customer_segment_prerequisite_ids: number[];
      prerequisite_customer_ids: number[];
      prerequisite_subtotal_range: { greater_than_or_equal_to: string } | null;
      prerequisite_quantity_range: { greater_than_or_equal_to: number } | null;
      prerequisite_shipping_price_range: { less_than_or_equal_to: string } | null;
      prerequisite_to_entitlement_quantity_ratio: {
        prerequisite_quantity: number | null;
        entitled_quantity: number | null;
      };
      prerequisite_to_entitlement_purchase: {
        prerequisite_amount: string | null;
      };
      title: string;
      admin_graphql_api_id: string;
    };
 }
export type {
  ShopifyCustomerMetafile,
  ShopifyCustomer,
  DiscountCode,
  PriceRule,
  PriceRuleResponse
}
