// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Organization {
  id                               String         @id @default(auto()) @map("_id") @db.ObjectId
  shop                             String         @unique
  organizationId                   String
  regionId                         String
  createdAt                        DateTime       @default(now())
  updatedAt                        DateTime       @updatedAt
  userId                           String?
  clientId                         String?
  clientSecret                     String?
  merchantId                       String
  merchantLocationId               String
  redeemPointsSubTransactionTypeId String?
  configuration                    Configuration?

  @@map("organizations")
}
type Configuration {
  freeShippingEligibleTiers        String[]
  freeShippingDiscountFunctionCode String?  @default("")
}

model OrganizationCreateStatus {
  id                               String   @id @default(auto()) @map("_id") @db.ObjectId
  shop                             String   @unique
  organizationId                   String?
  organizationCreated              Boolean  @default(false)
  regionId                         String?
  rootUserId                       String?
  permissionGroupId                String?
  integrationId                    String?
  integrationClientId              String?
  integrationClientSecret          String?
  assignedPermissions              Boolean  @default(false)
  email                            String?
  password                         String?
  merchantId                       String?
  merchantLocationId               String?
  createdAt                        DateTime @default(now())
  updatedAt                        DateTime @updatedAt
  redeemPointsSubTransactionTypeId String?
  pointRuleId                      String?

  @@map("organization_create_status")
}

model LoyaltySession {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  shop      String   @unique
  token     String
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("loyalty_sessions")
}
